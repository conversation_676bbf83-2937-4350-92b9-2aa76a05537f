^C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\CMAKEFILES\628366C1ADB751F267828C1BC262AE01\COMPLIAUDIOPROCESSOR_RESOURCES.RC.RULE
setlocal
JUCE\tools\extras\Build\juceaide\juceaide_artefacts\Custom\juceaide.exe rcfile C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/Info.txt C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKELISTS.TXT
setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
