// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		006DF460F8DF66EFFA80D968 /* Icon.icns */ = {isa = PBXBuildFile; fileRef = 70F1CAF3C4C561DD81E6AFC1; };
		0977FEC02DAF29438583198A /* include_juce_core.mm */ = {isa = PBXBuildFile; fileRef = 01E0EEF68A11C1CAF180E173; };
		0E041BED84BAC24200949A78 /* UniformTypeIdentifiers.framework */ = {isa = PBXBuildFile; fileRef = 961965555B4DAA5BE2361933; settings = { ATTRIBUTES = (Weak, ); }; };
		0E39AB2B15DCE39E1055A646 /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXBuildFile; fileRef = AB2DE62887E2F58D821F3217; };
		0FA2A3321630EBE83E439D99 /* include_juce_cryptography.mm */ = {isa = PBXBuildFile; fileRef = AFF729977947528F3E4AAA96; };
		1282A62308CD1AC3F88A5D03 /* Images.xcassets */ = {isa = PBXBuildFile; fileRef = 5273768FBB55D0DD57A5E70C; };
		1F7A8BD2B43B3D191132301D /* CoreImage.framework */ = {isa = PBXBuildFile; fileRef = E51ABCA80B75F33848F28184; };
		204FE224D562F0519DE438A4 /* include_juce_core_CompilationTime.cpp */ = {isa = PBXBuildFile; fileRef = BD85090C86849423E95A0014; };
		2E28F61A64DEF942FE7B94C4 /* include_juce_audio_processors.mm */ = {isa = PBXBuildFile; fileRef = AED58461CE961C62A0E0A552; };
		366A216FDEBD7BDDC1BA12D9 /* CoreGraphics.framework */ = {isa = PBXBuildFile; fileRef = DA40ED39AF4B56000E5A2743; };
		3717B9F9A0F7C9CB95F1BE7F /* include_juce_gui_extra.mm */ = {isa = PBXBuildFile; fileRef = 7BE6330821794919A88ED8ED; };
		3CC6DC6E223415B506D0CB75 /* AVFoundation.framework */ = {isa = PBXBuildFile; fileRef = 080961C54C58ECF2346B4C23; };
		5A64B64E5B45AEA1A0EECC4B /* CoreText.framework */ = {isa = PBXBuildFile; fileRef = 9193D2A3C463BEAA07FD424D; };
		5FF49672946F9857D0566A06 /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXBuildFile; fileRef = 53EC217F09F0762DFA2910A8; };
		61B523C52EBA17F738FFE31A /* include_juce_opengl.mm */ = {isa = PBXBuildFile; fileRef = 660F1970CF687A7AE8371C6D; };
		64DEB67F9523F28D899D1821 /* MetalKit.framework */ = {isa = PBXBuildFile; fileRef = C8C4E9A4028028FF1F5B76F2; settings = { ATTRIBUTES = (Weak, ); }; };
		67DF295E93E54432043126DF /* CoreAudio.framework */ = {isa = PBXBuildFile; fileRef = 6799B056504F9F017998B9E2; };
		6C2200C52B65E1BE80544E50 /* include_juce_audio_devices.mm */ = {isa = PBXBuildFile; fileRef = AF330F41D1A4865108690E3C; };
		770AB74B1D3A0108F764DD47 /* CoreAudioKit.framework */ = {isa = PBXBuildFile; fileRef = 4D1DB6D77B6F3DE7A569780B; };
		78CB229C1BA5093078BC6195 /* UserNotifications.framework */ = {isa = PBXBuildFile; fileRef = E8976208A3585295BF93D50D; settings = { ATTRIBUTES = (Weak, ); }; };
		80EE2C27B466BAFD83881D3F /* Accelerate.framework */ = {isa = PBXBuildFile; fileRef = 2E13A899F4E3C99054A3656F; };
		8ECB0767EE340DD83869E37D /* WebKit.framework */ = {isa = PBXBuildFile; fileRef = EC794872987FEA2E129C589A; };
		95C7D26CC68839FC6BF90AC3 /* include_juce_audio_processors_ara.cpp */ = {isa = PBXBuildFile; fileRef = 52EF9BE720EFF47106DB0351; };
		987CBD5330E76B404F0D966C /* Main.cpp */ = {isa = PBXBuildFile; fileRef = 77C0AC21C1028911123844FC; };
		9EFD2AA2FFF3C125FDAA4279 /* include_juce_data_structures.mm */ = {isa = PBXBuildFile; fileRef = 7525879E73E8AF32FFA0CDDE; };
		9F618C008A503063D10076C4 /* BinaryData.cpp */ = {isa = PBXBuildFile; fileRef = 74711D7544168CCAC4969A07; };
		A1F34D09F4E4338775917ED1 /* include_juce_audio_formats.mm */ = {isa = PBXBuildFile; fileRef = C6E2284D86D93F1D9D5C7666; };
		A6AA70BD9364BB974CDEB337 /* OpenGLES.framework */ = {isa = PBXBuildFile; fileRef = C821C5805007FFDC2636BBE6; };
		AC37CDB417E2D3FE619E5944 /* Metal.framework */ = {isa = PBXBuildFile; fileRef = B57F11A8AC0C8D3A8BF3BDCF; settings = { ATTRIBUTES = (Weak, ); }; };
		BB9A9692D99DD0DDB1047B60 /* include_juce_audio_basics.mm */ = {isa = PBXBuildFile; fileRef = 6D1F9E505D20C09647124F0A; };
		C4D6C466C41173D6970553D2 /* AudioToolbox.framework */ = {isa = PBXBuildFile; fileRef = 9E8129263CD42C6029FC2CAD; };
		C5E7BAD864E02CF37F7BD707 /* include_juce_events.mm */ = {isa = PBXBuildFile; fileRef = 33AA348465F512DBA8778DAF; };
		C6348C6B1D0312580E97EA19 /* include_juce_osc.cpp */ = {isa = PBXBuildFile; fileRef = 3BF06B70407FFDBE9534F942; };
		CA694B2A73FCF12D7F9E7E49 /* CoreMIDI.framework */ = {isa = PBXBuildFile; fileRef = 448838BE6E937D450A3C84CE; };
		CADEA83EAAC94E0011C07908 /* include_juce_gui_basics.mm */ = {isa = PBXBuildFile; fileRef = 84B287BB2AD252B7D69AC47E; };
		CBC8F7E5225C73CEDFB3B72E /* include_juce_graphics.mm */ = {isa = PBXBuildFile; fileRef = A7FF2B353C8568B5A7A80117; };
		D832165EE981EF309D4B21BF /* LaunchScreen.storyboard */ = {isa = PBXBuildFile; fileRef = 9D2DAC7D0C9DB77CB83E2992; };
		E29AB6243FBBDDD7BD723340 /* CoreServices.framework */ = {isa = PBXBuildFile; fileRef = EA5D4F9D63C0ED79CDE8967A; };
		EA153740F801BC51EFD75A5A /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXBuildFile; fileRef = AC684ED3A008C6E513200C05; };
		EA487FA4116517A8DFEE85B0 /* include_juce_audio_utils.mm */ = {isa = PBXBuildFile; fileRef = FCEBB157FB526741DB6791D1; };
		EC14DA30C090DDC62084DB4C /* QuartzCore.framework */ = {isa = PBXBuildFile; fileRef = 935CA85EF98714D3A17AE737; };
		F468E8C7B02DFD4D53911277 /* App */ = {isa = PBXBuildFile; fileRef = E4162459ED4C829EF7B19691; };
		F714F0C84F5945BF3539239E /* UIKit.framework */ = {isa = PBXBuildFile; fileRef = 379F77D23BFAE3795282CEB3; };
		F918FB5901F09EA77DB32022 /* Foundation.framework */ = {isa = PBXBuildFile; fileRef = F7D557738137CA1A370BAA27; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		01E0EEF68A11C1CAF180E173 /* include_juce_core.mm */ /* include_juce_core.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_core.mm; path = ../../JuceLibraryCode/include_juce_core.mm; sourceTree = SOURCE_ROOT; };
		080961C54C58ECF2346B4C23 /* AVFoundation.framework */ /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		0ADF0DECFCB1DB4D3A847EB5 /* juce_icon.png */ /* juce_icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = juce_icon.png; path = ../../Source/juce_icon.png; sourceTree = SOURCE_ROOT; };
		18BFEBD944DDE4809C89F27A /* juce_graphics */ /* juce_graphics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_graphics; path = ../../../../modules/juce_graphics; sourceTree = SOURCE_ROOT; };
		1FD6DBAC73414DD4C152E34E /* Info-App.plist */ /* Info-App.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-App.plist"; path = "Info-App.plist"; sourceTree = SOURCE_ROOT; };
		25DEDA8C9F94A6C8DFC8E53E /* SharedCanvas.h */ /* SharedCanvas.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = SharedCanvas.h; path = ../../Source/SharedCanvas.h; sourceTree = SOURCE_ROOT; };
		2E13A899F4E3C99054A3656F /* Accelerate.framework */ /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		33AA348465F512DBA8778DAF /* include_juce_events.mm */ /* include_juce_events.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_events.mm; path = ../../JuceLibraryCode/include_juce_events.mm; sourceTree = SOURCE_ROOT; };
		379F77D23BFAE3795282CEB3 /* UIKit.framework */ /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		3BF06B70407FFDBE9534F942 /* include_juce_osc.cpp */ /* include_juce_osc.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_osc.cpp; path = ../../JuceLibraryCode/include_juce_osc.cpp; sourceTree = SOURCE_ROOT; };
		448838BE6E937D450A3C84CE /* CoreMIDI.framework */ /* CoreMIDI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMIDI.framework; path = System/Library/Frameworks/CoreMIDI.framework; sourceTree = SDKROOT; };
		4D1DB6D77B6F3DE7A569780B /* CoreAudioKit.framework */ /* CoreAudioKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioKit.framework; path = System/Library/Frameworks/CoreAudioKit.framework; sourceTree = SDKROOT; };
		4FF648D72D6F1A78956CDA1B /* Demos.h */ /* Demos.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Demos.h; path = ../../Source/Demos.h; sourceTree = SOURCE_ROOT; };
		5273768FBB55D0DD57A5E70C /* Images.xcassets */ /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = NetworkGraphicsDemo/Images.xcassets; sourceTree = SOURCE_ROOT; };
		52EF9BE720EFF47106DB0351 /* include_juce_audio_processors_ara.cpp */ /* include_juce_audio_processors_ara.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_ara.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_ara.cpp; sourceTree = SOURCE_ROOT; };
		53EC217F09F0762DFA2910A8 /* include_juce_graphics_Sheenbidi.c */ /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = include_juce_graphics_Sheenbidi.c; path = ../../JuceLibraryCode/include_juce_graphics_Sheenbidi.c; sourceTree = SOURCE_ROOT; };
		55CB060922ABCBC105FE38D2 /* juce_osc */ /* juce_osc */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_osc; path = ../../../../modules/juce_osc; sourceTree = SOURCE_ROOT; };
		660F1970CF687A7AE8371C6D /* include_juce_opengl.mm */ /* include_juce_opengl.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_opengl.mm; path = ../../JuceLibraryCode/include_juce_opengl.mm; sourceTree = SOURCE_ROOT; };
		6799B056504F9F017998B9E2 /* CoreAudio.framework */ /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		68EBC0BF5F01E05FDCB3EEAF /* juce_opengl */ /* juce_opengl */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_opengl; path = ../../../../modules/juce_opengl; sourceTree = SOURCE_ROOT; };
		6D1F9E505D20C09647124F0A /* include_juce_audio_basics.mm */ /* include_juce_audio_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_basics.mm; path = ../../JuceLibraryCode/include_juce_audio_basics.mm; sourceTree = SOURCE_ROOT; };
		70F1CAF3C4C561DD81E6AFC1 /* Icon.icns */ /* Icon.icns */ = {isa = PBXFileReference; lastKnownFileType = file.icns; name = Icon.icns; path = Icon.icns; sourceTree = SOURCE_ROOT; };
		74711D7544168CCAC4969A07 /* BinaryData.cpp */ /* BinaryData.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = BinaryData.cpp; path = ../../JuceLibraryCode/BinaryData.cpp; sourceTree = SOURCE_ROOT; };
		7525879E73E8AF32FFA0CDDE /* include_juce_data_structures.mm */ /* include_juce_data_structures.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_data_structures.mm; path = ../../JuceLibraryCode/include_juce_data_structures.mm; sourceTree = SOURCE_ROOT; };
		77C0AC21C1028911123844FC /* Main.cpp */ /* Main.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Main.cpp; path = ../../Source/Main.cpp; sourceTree = SOURCE_ROOT; };
		7BE6330821794919A88ED8ED /* include_juce_gui_extra.mm */ /* include_juce_gui_extra.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_extra.mm; path = ../../JuceLibraryCode/include_juce_gui_extra.mm; sourceTree = SOURCE_ROOT; };
		84B287BB2AD252B7D69AC47E /* include_juce_gui_basics.mm */ /* include_juce_gui_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_basics.mm; path = ../../JuceLibraryCode/include_juce_gui_basics.mm; sourceTree = SOURCE_ROOT; };
		89583CD42AD218E9753DF11C /* juce_audio_devices */ /* juce_audio_devices */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_devices; path = ../../../../modules/juce_audio_devices; sourceTree = SOURCE_ROOT; };
		8EACAADD3A23DED3E252C92F /* juce_core */ /* juce_core */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_core; path = ../../../../modules/juce_core; sourceTree = SOURCE_ROOT; };
		9193D2A3C463BEAA07FD424D /* CoreText.framework */ /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		92800676AF753D1A60108F11 /* BinaryData.h */ /* BinaryData.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = BinaryData.h; path = ../../JuceLibraryCode/BinaryData.h; sourceTree = SOURCE_ROOT; };
		935CA85EF98714D3A17AE737 /* QuartzCore.framework */ /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		961965555B4DAA5BE2361933 /* UniformTypeIdentifiers.framework */ /* UniformTypeIdentifiers.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UniformTypeIdentifiers.framework; path = System/Library/Frameworks/UniformTypeIdentifiers.framework; sourceTree = SDKROOT; };
		9982F39121710EFFD5FEEAEF /* MasterComponent.h */ /* MasterComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = MasterComponent.h; path = ../../Source/MasterComponent.h; sourceTree = SOURCE_ROOT; };
		9C67BD1915C7FD5747C2BA8F /* juce_audio_formats */ /* juce_audio_formats */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_formats; path = ../../../../modules/juce_audio_formats; sourceTree = SOURCE_ROOT; };
		9C689AFBF364CB167C422D29 /* juce_gui_extra */ /* juce_gui_extra */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_extra; path = ../../../../modules/juce_gui_extra; sourceTree = SOURCE_ROOT; };
		9D2DAC7D0C9DB77CB83E2992 /* LaunchScreen.storyboard */ /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = LaunchScreen.storyboard; sourceTree = SOURCE_ROOT; };
		9E8129263CD42C6029FC2CAD /* AudioToolbox.framework */ /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		A505E1DABB2ED630EFBA96DB /* juce_audio_processors */ /* juce_audio_processors */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_processors; path = ../../../../modules/juce_audio_processors; sourceTree = SOURCE_ROOT; };
		A7FF2B353C8568B5A7A80117 /* include_juce_graphics.mm */ /* include_juce_graphics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_graphics.mm; path = ../../JuceLibraryCode/include_juce_graphics.mm; sourceTree = SOURCE_ROOT; };
		AB2DE62887E2F58D821F3217 /* include_juce_audio_processors_lv2_libs.cpp */ /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_lv2_libs.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_lv2_libs.cpp; sourceTree = SOURCE_ROOT; };
		AC684ED3A008C6E513200C05 /* include_juce_graphics_Harfbuzz.cpp */ /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_graphics_Harfbuzz.cpp; path = ../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp; sourceTree = SOURCE_ROOT; };
		AED58461CE961C62A0E0A552 /* include_juce_audio_processors.mm */ /* include_juce_audio_processors.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_processors.mm; path = ../../JuceLibraryCode/include_juce_audio_processors.mm; sourceTree = SOURCE_ROOT; };
		AF330F41D1A4865108690E3C /* include_juce_audio_devices.mm */ /* include_juce_audio_devices.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_devices.mm; path = ../../JuceLibraryCode/include_juce_audio_devices.mm; sourceTree = SOURCE_ROOT; };
		AFF729977947528F3E4AAA96 /* include_juce_cryptography.mm */ /* include_juce_cryptography.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_cryptography.mm; path = ../../JuceLibraryCode/include_juce_cryptography.mm; sourceTree = SOURCE_ROOT; };
		B5433B00F012AD87AADBFCD6 /* juce_cryptography */ /* juce_cryptography */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_cryptography; path = ../../../../modules/juce_cryptography; sourceTree = SOURCE_ROOT; };
		B57F11A8AC0C8D3A8BF3BDCF /* Metal.framework */ /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		B76F10A7778664E164A01934 /* juce_audio_basics */ /* juce_audio_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_basics; path = ../../../../modules/juce_audio_basics; sourceTree = SOURCE_ROOT; };
		B9B80E3572715F63FFC3678B /* ClientComponent.h */ /* ClientComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = ClientComponent.h; path = ../../Source/ClientComponent.h; sourceTree = SOURCE_ROOT; };
		BA2E40409255F1B078406221 /* juce_data_structures */ /* juce_data_structures */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_data_structures; path = ../../../../modules/juce_data_structures; sourceTree = SOURCE_ROOT; };
		BD85090C86849423E95A0014 /* include_juce_core_CompilationTime.cpp */ /* include_juce_core_CompilationTime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_core_CompilationTime.cpp; path = ../../JuceLibraryCode/include_juce_core_CompilationTime.cpp; sourceTree = SOURCE_ROOT; };
		C6E2284D86D93F1D9D5C7666 /* include_juce_audio_formats.mm */ /* include_juce_audio_formats.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_formats.mm; path = ../../JuceLibraryCode/include_juce_audio_formats.mm; sourceTree = SOURCE_ROOT; };
		C821C5805007FFDC2636BBE6 /* OpenGLES.framework */ /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		C8C4E9A4028028FF1F5B76F2 /* MetalKit.framework */ /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		D12A0DFFE18728E84D9AB739 /* JuceHeader.h */ /* JuceHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JuceHeader.h; path = ../../JuceLibraryCode/JuceHeader.h; sourceTree = SOURCE_ROOT; };
		DA40ED39AF4B56000E5A2743 /* CoreGraphics.framework */ /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		E4162459ED4C829EF7B19691 /* App */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "JUCE Network Graphics Demo.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		E4EA870A22A0C6649E55DD30 /* juce_audio_utils */ /* juce_audio_utils */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_utils; path = ../../../../modules/juce_audio_utils; sourceTree = SOURCE_ROOT; };
		E51ABCA80B75F33848F28184 /* CoreImage.framework */ /* CoreImage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreImage.framework; path = System/Library/Frameworks/CoreImage.framework; sourceTree = SDKROOT; };
		E8976208A3585295BF93D50D /* UserNotifications.framework */ /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		EA5D4F9D63C0ED79CDE8967A /* CoreServices.framework */ /* CoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreServices.framework; path = System/Library/Frameworks/CoreServices.framework; sourceTree = SDKROOT; };
		EC794872987FEA2E129C589A /* WebKit.framework */ /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		F7D557738137CA1A370BAA27 /* Foundation.framework */ /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		F98A4DAA0502EE9252EBE06F /* juce_events */ /* juce_events */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_events; path = ../../../../modules/juce_events; sourceTree = SOURCE_ROOT; };
		FA0A789443FD480003E40435 /* juce_gui_basics */ /* juce_gui_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_basics; path = ../../../../modules/juce_gui_basics; sourceTree = SOURCE_ROOT; };
		FCEBB157FB526741DB6791D1 /* include_juce_audio_utils.mm */ /* include_juce_audio_utils.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_utils.mm; path = ../../JuceLibraryCode/include_juce_audio_utils.mm; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		865E89B94B41EB14C202CBB0 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				80EE2C27B466BAFD83881D3F,
				C4D6C466C41173D6970553D2,
				3CC6DC6E223415B506D0CB75,
				67DF295E93E54432043126DF,
				770AB74B1D3A0108F764DD47,
				366A216FDEBD7BDDC1BA12D9,
				1F7A8BD2B43B3D191132301D,
				CA694B2A73FCF12D7F9E7E49,
				E29AB6243FBBDDD7BD723340,
				5A64B64E5B45AEA1A0EECC4B,
				F918FB5901F09EA77DB32022,
				A6AA70BD9364BB974CDEB337,
				EC14DA30C090DDC62084DB4C,
				F714F0C84F5945BF3539239E,
				8ECB0767EE340DD83869E37D,
				AC37CDB417E2D3FE619E5944,
				64DEB67F9523F28D899D1821,
				0E041BED84BAC24200949A78,
				78CB229C1BA5093078BC6195,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4AA57E2B5FD5374D348EEF7F /* Resources */ = {
			isa = PBXGroup;
			children = (
				1FD6DBAC73414DD4C152E34E,
				5273768FBB55D0DD57A5E70C,
				9D2DAC7D0C9DB77CB83E2992,
				70F1CAF3C4C561DD81E6AFC1,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		5A58AF0A052C539F0E342A88 /* Source */ = {
			isa = PBXGroup;
			children = (
				4FF648D72D6F1A78956CDA1B,
				77C0AC21C1028911123844FC,
				9982F39121710EFFD5FEEAEF,
				B9B80E3572715F63FFC3678B,
				25DEDA8C9F94A6C8DFC8E53E,
			);
			name = Source;
			sourceTree = "<group>";
		};
		71F5560BE0EE3A17A9CE44F6 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				2E13A899F4E3C99054A3656F,
				9E8129263CD42C6029FC2CAD,
				080961C54C58ECF2346B4C23,
				6799B056504F9F017998B9E2,
				4D1DB6D77B6F3DE7A569780B,
				DA40ED39AF4B56000E5A2743,
				E51ABCA80B75F33848F28184,
				448838BE6E937D450A3C84CE,
				EA5D4F9D63C0ED79CDE8967A,
				9193D2A3C463BEAA07FD424D,
				F7D557738137CA1A370BAA27,
				C821C5805007FFDC2636BBE6,
				935CA85EF98714D3A17AE737,
				379F77D23BFAE3795282CEB3,
				EC794872987FEA2E129C589A,
				B57F11A8AC0C8D3A8BF3BDCF,
				C8C4E9A4028028FF1F5B76F2,
				961965555B4DAA5BE2361933,
				E8976208A3585295BF93D50D,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		856518502A948813C90E6761 /* NetworkGraphicsDemo */ = {
			isa = PBXGroup;
			children = (
				5A58AF0A052C539F0E342A88,
				0ADF0DECFCB1DB4D3A847EB5,
			);
			name = NetworkGraphicsDemo;
			sourceTree = "<group>";
		};
		8B59A884C62D960EE9DFEF47 /* Products */ = {
			isa = PBXGroup;
			children = (
				E4162459ED4C829EF7B19691,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C20D4DECE1291BF6AF8711DC /* JUCE Library Code */ = {
			isa = PBXGroup;
			children = (
				74711D7544168CCAC4969A07,
				92800676AF753D1A60108F11,
				6D1F9E505D20C09647124F0A,
				AF330F41D1A4865108690E3C,
				C6E2284D86D93F1D9D5C7666,
				AED58461CE961C62A0E0A552,
				52EF9BE720EFF47106DB0351,
				AB2DE62887E2F58D821F3217,
				FCEBB157FB526741DB6791D1,
				01E0EEF68A11C1CAF180E173,
				BD85090C86849423E95A0014,
				AFF729977947528F3E4AAA96,
				7525879E73E8AF32FFA0CDDE,
				33AA348465F512DBA8778DAF,
				A7FF2B353C8568B5A7A80117,
				AC684ED3A008C6E513200C05,
				53EC217F09F0762DFA2910A8,
				84B287BB2AD252B7D69AC47E,
				7BE6330821794919A88ED8ED,
				660F1970CF687A7AE8371C6D,
				3BF06B70407FFDBE9534F942,
				D12A0DFFE18728E84D9AB739,
			);
			name = "JUCE Library Code";
			sourceTree = "<group>";
		};
		D2EB65517396C974F0415A7F /* Source */ = {
			isa = PBXGroup;
			children = (
				856518502A948813C90E6761,
				EF0AACA84F1CEE7F45F56339,
				C20D4DECE1291BF6AF8711DC,
				4AA57E2B5FD5374D348EEF7F,
				71F5560BE0EE3A17A9CE44F6,
				8B59A884C62D960EE9DFEF47,
			);
			name = Source;
			sourceTree = "<group>";
		};
		EF0AACA84F1CEE7F45F56339 /* JUCE Modules */ = {
			isa = PBXGroup;
			children = (
				B76F10A7778664E164A01934,
				89583CD42AD218E9753DF11C,
				9C67BD1915C7FD5747C2BA8F,
				A505E1DABB2ED630EFBA96DB,
				E4EA870A22A0C6649E55DD30,
				8EACAADD3A23DED3E252C92F,
				B5433B00F012AD87AADBFCD6,
				BA2E40409255F1B078406221,
				F98A4DAA0502EE9252EBE06F,
				18BFEBD944DDE4809C89F27A,
				FA0A789443FD480003E40435,
				9C689AFBF364CB167C422D29,
				68EBC0BF5F01E05FDCB3EEAF,
				55CB060922ABCBC105FE38D2,
			);
			name = "JUCE Modules";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		4311FBCBD02948A0ED96C7DD /* NetworkGraphicsDemo - App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B73863F5D180C23D3EC40C38;
			buildPhases = (
				714944DB86A4D402E7FA269E,
				C2977559BF9148DB70CA10AE,
				865E89B94B41EB14C202CBB0,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "NetworkGraphicsDemo - App";
			productName = NetworkGraphicsDemo;
			productReference = E4162459ED4C829EF7B19691;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A5398ADB6F5B128C00EB935C = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = "Raw Material Software Limited";
				TargetAttributes = {
					4311FBCBD02948A0ED96C7DD = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
				};
			};
			buildConfigurationList = 02715337C584F3C721251428;
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = D2EB65517396C974F0415A7F;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				4311FBCBD02948A0ED96C7DD,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		714944DB86A4D402E7FA269E = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1282A62308CD1AC3F88A5D03,
				D832165EE981EF309D4B21BF,
				006DF460F8DF66EFFA80D968,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C2977559BF9148DB70CA10AE = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				987CBD5330E76B404F0D966C,
				9F618C008A503063D10076C4,
				BB9A9692D99DD0DDB1047B60,
				6C2200C52B65E1BE80544E50,
				A1F34D09F4E4338775917ED1,
				2E28F61A64DEF942FE7B94C4,
				95C7D26CC68839FC6BF90AC3,
				0E39AB2B15DCE39E1055A646,
				EA487FA4116517A8DFEE85B0,
				0977FEC02DAF29438583198A,
				204FE224D562F0519DE438A4,
				0FA2A3321630EBE83E439D99,
				9EFD2AA2FFF3C125FDAA4279,
				C5E7BAD864E02CF37F7BD707,
				CBC8F7E5225C73CEDFB3B72E,
				EA153740F801BC51EFD75A5A,
				5FF49672946F9857D0566A06,
				CADEA83EAAC94E0011C07908,
				3717B9F9A0F7C9CB95F1BE7F,
				61B523C52EBA17F738FFE31A,
				C6348C6B1D0312580E97EA19,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2E06386CE7CCA5FF76819BFF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_CONTENT_SHARING=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_cryptography=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCER_XCODE_IPHONE_5BC26AE3=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-App.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Applications";
				LLVM_LTO = YES;
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				PRODUCT_BUNDLE_IDENTIFIER = com.juce.NetworkGraphicsDemo;
				PRODUCT_NAME = "JUCE Network Graphics Demo";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGLES;
			};
			name = Release;
		};
		3BF0365A560ACD4FD24D40CE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "JUCE Network Graphics Demo";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Debug;
		};
		9C6D2FD441D79104734762A5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = "JUCE Network Graphics Demo";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Release;
		};
		EE7498599191DDC73ECB55B0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_CONTENT_SHARING=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_cryptography=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCER_XCODE_IPHONE_5BC26AE3=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-App.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Applications";
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				PRODUCT_BUNDLE_IDENTIFIER = com.juce.NetworkGraphicsDemo;
				PRODUCT_NAME = "JUCE Network Graphics Demo";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGLES;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		02715337C584F3C721251428 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				3BF0365A560ACD4FD24D40CE,
				9C6D2FD441D79104734762A5,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		B73863F5D180C23D3EC40C38 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EE7498599191DDC73ECB55B0,
				2E06386CE7CCA5FF76819BFF,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = A5398ADB6F5B128C00EB935C /* Project object */;
}
