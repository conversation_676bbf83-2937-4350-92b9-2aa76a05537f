#pragma code_page(65001)

#ifdef JUCE_USER_DEFINED_RC_FILE
 #include JUCE_USER_DEFINED_RC_FILE
#else

#undef  WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#include <windows.h>

VS_VERSION_INFO VERSIONINFO
FILEVERSION  1,0,0,0
BEGIN
  BLOCK "StringFileInfo"
  BEGIN
    BLOCK "040904E4"
    BEGIN
      VALUE "CompanyName",  "Raw Material Software Limited\0"
      VALUE "LegalCopyright",  "Raw Material Software Limited\0"
      VALUE "FileDescription",  "UnitTestRunner\0"
      VALUE "FileVersion",  "1.0.0\0"
      VALUE "ProductName",  "UnitTestRunner\0"
      VALUE "ProductVersion",  "1.0.0\0"
    END
  END

  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x409, 1252
  END
END

#endif
