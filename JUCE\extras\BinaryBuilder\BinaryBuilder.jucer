<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT id="3t6YqETY1" name="BinaryBuilder" projectType="consoleapp"
              juceFolder="../../../juce" bundleIdentifier="com.juce.binarybuilder"
              companyName="Raw Material Software Limited"
              companyCopyright="Raw Material Software Limited" useAppConfig="0"
              addUsingNamespaceToJuceHeader="1" jucerFormatVersion="1">
  <EXPORTFORMATS>
    <XCODE_MAC targetFolder="Builds/MacOSX" applicationCategory="public.app-category.developer-tools"
               extraDefs="JUCE_SILENCE_XCODE_15_LINKER_WARNING=1" extraLinkerFlags="-Wl,-weak_reference_mismatches,weak">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="BinaryBuilder"/>
        <CONFIGURATION name="Release" isDebug="0" optimisation="2" targetName="BinaryBuilder"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_MAC>
    <LINUX_MAKE targetFolder="Builds/LinuxMakefile">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="BinaryBuilder"/>
        <CONFIGURATION name="Release" isDebug="0" optimisation="2" targetName="BinaryBuilder"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
      </MODULEPATHS>
    </LINUX_MAKE>
    <VS2022 targetFolder="Builds/VisualStudio2022">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="BinaryBuilder"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="BinaryBuilder"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
      </MODULEPATHS>
    </VS2022>
  </EXPORTFORMATS>
  <MAINGROUP id="TTBP5JihO" name="BinaryBuilder">
    <GROUP id="noYkBuTg" name="Source">
      <FILE id="4QwydNA9f" name="Main.cpp" compile="1" resource="0" file="Source/Main.cpp"/>
    </GROUP>
  </MAINGROUP>
  <JUCEOPTIONS/>
  <MODULES>
    <MODULE id="juce_core" showAllCode="1"/>
  </MODULES>
  <LIVE_SETTINGS>
    <OSX/>
  </LIVE_SETTINGS>
</JUCERPROJECT>
