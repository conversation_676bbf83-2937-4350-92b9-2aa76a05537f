^C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\CMAKEFILES\5D1E0A3A22D9EBF7D408198184D9F03F\GENERATE.STAMP.RULE
setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/COMPLI.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
