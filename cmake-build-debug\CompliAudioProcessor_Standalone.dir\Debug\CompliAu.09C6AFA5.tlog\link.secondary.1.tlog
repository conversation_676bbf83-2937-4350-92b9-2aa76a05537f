^C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_RC_LIB.DIR\DEBUG\COMPLIAUDIOPROCESSOR_RESOURCES.RES|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_AAX.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_AAX_UTILS.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_ARA.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_LV2.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_STANDALONE.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_UNITY.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_VST2.OBJ|C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKE-BUILD-DEBUG\COMPLIAUDIOPROCESSOR_STANDALONE.DIR\DEBUG\JUCE_AUDIO_PLUGIN_CLIENT_VST3.OBJ
C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_Standalone.dir\Debug\compli.ilk
