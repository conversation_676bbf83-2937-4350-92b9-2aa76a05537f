C:\Users\<USER>\Documents\GitHub\compli2\PluginEditor.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\PluginEditor.obj
C:\Users\<USER>\Documents\GitHub\compli2\PluginProcessor.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\PluginProcessor.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_processors\juce_audio_processors.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_processors.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_processors\juce_audio_processors_ara.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_processors_ara.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_processors\juce_audio_processors_lv2_libs.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_processors_lv2_libs.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_gui_extra\juce_gui_extra.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_gui_extra.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_gui_basics\juce_gui_basics.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_gui_basics.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_graphics\juce_graphics.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_graphics.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_graphics\juce_graphics_Harfbuzz.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_graphics_Harfbuzz.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_graphics\juce_graphics_Sheenbidi.c;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_graphics_Sheenbidi.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_events\juce_events.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_events.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_core\juce_core.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_core.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_core\juce_core_CompilationTime.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_core_CompilationTime.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_data_structures\juce_data_structures.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_data_structures.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_basics\juce_audio_basics.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_basics.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_utils\juce_audio_utils.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_utils.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_formats\juce_audio_formats.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_formats.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_audio_devices\juce_audio_devices.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_audio_devices.obj
C:\Users\<USER>\Documents\GitHub\compli2\JUCE\modules\juce_dsp\juce_dsp.cpp;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor.dir\Debug\juce_dsp.obj
