name: check-<PERSON><PERSON>
on: [pull_request_target]
jobs:
  check-cla:
    runs-on: ubuntu-latest
    env:
      PR_NUMBER: ${{ github.event.number }}
    steps:
      - name: check-CLA
        if: github.repository == 'juce-framework/JUCE'
        run: |
          import urllib.request
          import json
          import sys
          def jsonRequest(url, data={}):
              req = urllib.request.Request(url,
                                           headers={'Content-Type': 'application/json'},
                                           data=json.dumps(data).encode('utf-8') if data else None)
              with urllib.request.urlopen(req) as response:
                  return json.loads(response.read().decode('utf-8'))
          prCommits = jsonRequest('https://api.github.com/repos/juce-framework/JUCE/pulls/${{ github.event.number }}/commits')
          print(f'Commit info:\n{json.dumps(prCommits, indent=4)}')
          allAuthors = [commit[authorType]['login'] for authorType in ['author', 'committer'] for commit in prCommits if commit[authorType]]
          uniqueAuthors = [name for name in list(set(allAuthors)) if name != 'web-flow']
          if (len(uniqueAuthors) == 0):
              print(f'\nNo author or committer user IDs contained within commit information\n\n{prCommits}\n')
              sys.exit(1)
          print(f'Authors: <AUTHORS>
          claResult = jsonRequest('https://cla.juce.com/check', {'logins': uniqueAuthors})
          unsignedLogins = claResult['unsigned']
          if (len(unsignedLogins) != 0):
              print(f'\nThe following GitHub users need to sign the JUCE CLA: {", ".join(unsignedLogins)}\n\nPlease go to https://cla.juce.com to sign the JUCE Contributor Licence Agreement\n')
              sys.exit(1)
        shell: python
