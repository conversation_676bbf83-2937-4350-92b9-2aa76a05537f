// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0DA13944CF8AD7F862DF03E3 /* include_juce_analytics.cpp */ = {isa = PBXBuildFile; fileRef = 324682B2C8B9B4ACD9711A7A; };
		17A09B4AF453B148CD7349F4 /* OpenGL.framework */ = {isa = PBXBuildFile; fileRef = FCB76958E12B2D7F8277CD59; };
		1A038A2954FB9A4F208BE3F2 /* QuartzCore.framework */ = {isa = PBXBuildFile; fileRef = F260758DB97CF0F5C85218C1; };
		1B09834E81EAF5BCB87FAAF4 /* include_juce_product_unlocking.mm */ = {isa = PBXBuildFile; fileRef = B96EC82EC3D2813B50386198; };
		1D06F1A254F84A7AE3E90DF2 /* include_juce_opengl.mm */ = {isa = PBXBuildFile; fileRef = 1CA82C74AEC08421812BDCAC; };
		26064360DE64348CC75E6340 /* Metal.framework */ = {isa = PBXBuildFile; fileRef = 5B38ECC1B68681432732938C; settings = { ATTRIBUTES = (Weak, ); }; };
		263250D6F359CE403B0566FF /* IOKit.framework */ = {isa = PBXBuildFile; fileRef = 8C449538B266A891147103D6; };
		2AD7D2E1785FCABA09AE3764 /* Security.framework */ = {isa = PBXBuildFile; fileRef = 5CF6DD6C5477309A1E9AB644; };
		2DC417FBA2F857098B3502B2 /* include_juce_core_CompilationTime.cpp */ = {isa = PBXBuildFile; fileRef = 8048091E98040AC90FF4715D; };
		32010EA67EEFE024B9CE1CB5 /* DiscRecording.framework */ = {isa = PBXBuildFile; fileRef = B4202EE1243A8FCA29996D05; };
		33D24B475EA928745A87EDDB /* include_juce_audio_devices.mm */ = {isa = PBXBuildFile; fileRef = 00CDB93410EA5AECBA5ADA95; };
		36F598D91354C54D8B028843 /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXBuildFile; fileRef = DA5B8888560BA7B144F88DA5; };
		3822F598DA7044E5DB7633A9 /* include_juce_audio_utils.mm */ = {isa = PBXBuildFile; fileRef = 846E187EC2E797B982861CA4; };
		3866839F4051D104244870B1 /* CoreAudioKit.framework */ = {isa = PBXBuildFile; fileRef = 336A244E3C6460495F0A424C; };
		44628795CE784DEE1B98F986 /* include_juce_audio_processors_ara.cpp */ = {isa = PBXBuildFile; fileRef = C7F2704A6676859CCE14A4D1; };
		4BC57B0D2215621D90C8881C /* WebKit.framework */ = {isa = PBXBuildFile; fileRef = D2EBC6292AE5AFC46EB10DAC; };
		5CB3596030B0DD3763CAF85C /* include_juce_data_structures.mm */ = {isa = PBXBuildFile; fileRef = 302A999B2803C0D5C15D237C; };
		5FE50792EDC7638DE9A824B5 /* RecentFilesMenuTemplate.nib */ = {isa = PBXBuildFile; fileRef = 5C7BDD8DF72F2FC2D44D757A; };
		66FC7F44EEC9044E5C4A21C3 /* CoreAudio.framework */ = {isa = PBXBuildFile; fileRef = C0531453A002C480280C5F05; };
		69EB54A3097C15333ECB957A /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXBuildFile; fileRef = 0E5DB2C17DAC6067A0DAC84E; };
		7164274FE42C7EC423455E05 /* include_juce_osc.cpp */ = {isa = PBXBuildFile; fileRef = A59D9064C3A2D7EC3DC45420; };
		74EC8AEC296DB2721EB438BF /* include_juce_audio_processors.mm */ = {isa = PBXBuildFile; fileRef = 3A26A3568F2C301EEED25288; };
		79FE3F2D2EFAC333283E5D90 /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXBuildFile; fileRef = 45FB94C047D1ECAACA9462B7; };
		8931A330E8D2263E787BDBA6 /* include_juce_javascript.cpp */ = {isa = PBXBuildFile; fileRef = 46D161431FC09F290F176D30; };
		8D51903C59161885903F60CC /* CoreMIDI.framework */ = {isa = PBXBuildFile; fileRef = 04C1B8BF62AA09E62B362913; };
		96EFF7BA261F57DD829324D8 /* AudioToolbox.framework */ = {isa = PBXBuildFile; fileRef = 7898C73DCA6FA9D9CF669D32; };
		9B48039CDFD679AD944BAC70 /* include_juce_core.mm */ = {isa = PBXBuildFile; fileRef = AB19DDC8458D2A420E6D8AC3; };
		9E15F32DCE5118835FF71511 /* ConsoleApp */ = {isa = PBXBuildFile; fileRef = 080EAB9CF5AB2BD6B2BBB173; };
		9E31DAE99C88F8975034EA52 /* MetalKit.framework */ = {isa = PBXBuildFile; fileRef = 73E9BD8E156D7293C10C298B; settings = { ATTRIBUTES = (Weak, ); }; };
		A1A39E64F9E03EFFA10B0A10 /* include_juce_graphics.mm */ = {isa = PBXBuildFile; fileRef = EECBAA403D2D6AEEA8CB05EB; };
		A4C15593F5B4E1BE338F4F63 /* include_juce_events.mm */ = {isa = PBXBuildFile; fileRef = 3D169C5EFBF6304F5CE4C35E; };
		A70F7F4891DB1CF67653BE74 /* Accelerate.framework */ = {isa = PBXBuildFile; fileRef = B38A1AC42B002115350C0268; };
		AA207299991F85938465BF65 /* Cocoa.framework */ = {isa = PBXBuildFile; fileRef = 2030A589A9355FE6A0F72428; };
		AF1FE82A4A20DCB8944B35C7 /* include_juce_gui_extra.mm */ = {isa = PBXBuildFile; fileRef = 4195CB317C364D778AE2ADB1; };
		B407D123F08A9A8C12624ABA /* include_juce_midi_ci.cpp */ = {isa = PBXBuildFile; fileRef = 0EFA505235D959565503D537; };
		BFED026CA071070CEB87CFB5 /* include_juce_audio_basics.mm */ = {isa = PBXBuildFile; fileRef = 4BD792956FE7C22CB8FB691D; };
		D17BAE3D36BB94FC2C8E2438 /* Main.cpp */ = {isa = PBXBuildFile; fileRef = 88AA2B9840A6792BBAD559EE; };
		D43289CF624A7B068237C192 /* include_juce_gui_basics.mm */ = {isa = PBXBuildFile; fileRef = 583EA0E5C4B75A629AEF1157; };
		EB8BBB7D2DBDB1092CE236E9 /* Foundation.framework */ = {isa = PBXBuildFile; fileRef = D484C08AF83E4D2DFA030F1A; };
		FC139F56BD13A2C78D21076E /* include_juce_cryptography.mm */ = {isa = PBXBuildFile; fileRef = 08ED235CBE02E0FB4BE4653E; };
		FD15EF066F019B57F0601D71 /* include_juce_dsp.mm */ = {isa = PBXBuildFile; fileRef = 4CA19EC18C2BC536B3636842; };
		FDDF955477BE7FEBC364E19B /* include_juce_audio_formats.mm */ = {isa = PBXBuildFile; fileRef = A76DD7182C290A9020C96CA7; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		00CDB93410EA5AECBA5ADA95 /* include_juce_audio_devices.mm */ /* include_juce_audio_devices.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_devices.mm; path = ../../JuceLibraryCode/include_juce_audio_devices.mm; sourceTree = SOURCE_ROOT; };
		02D7ADAB1962C77DC0BDCC0E /* juce_javascript */ /* juce_javascript */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_javascript; path = ../../../../modules/juce_javascript; sourceTree = SOURCE_ROOT; };
		04C1B8BF62AA09E62B362913 /* CoreMIDI.framework */ /* CoreMIDI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMIDI.framework; path = System/Library/Frameworks/CoreMIDI.framework; sourceTree = SDKROOT; };
		05501801BF6C4A47598C59E2 /* juce_cryptography */ /* juce_cryptography */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_cryptography; path = ../../../../modules/juce_cryptography; sourceTree = SOURCE_ROOT; };
		080EAB9CF5AB2BD6B2BBB173 /* ConsoleApp */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = UnitTestRunner; sourceTree = BUILT_PRODUCTS_DIR; };
		08ED235CBE02E0FB4BE4653E /* include_juce_cryptography.mm */ /* include_juce_cryptography.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_cryptography.mm; path = ../../JuceLibraryCode/include_juce_cryptography.mm; sourceTree = SOURCE_ROOT; };
		0E5DB2C17DAC6067A0DAC84E /* include_juce_graphics_Sheenbidi.c */ /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = include_juce_graphics_Sheenbidi.c; path = ../../JuceLibraryCode/include_juce_graphics_Sheenbidi.c; sourceTree = SOURCE_ROOT; };
		0EFA505235D959565503D537 /* include_juce_midi_ci.cpp */ /* include_juce_midi_ci.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_midi_ci.cpp; path = ../../JuceLibraryCode/include_juce_midi_ci.cpp; sourceTree = SOURCE_ROOT; };
		1CA82C74AEC08421812BDCAC /* include_juce_opengl.mm */ /* include_juce_opengl.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_opengl.mm; path = ../../JuceLibraryCode/include_juce_opengl.mm; sourceTree = SOURCE_ROOT; };
		1DC921E6494548F5E73E1056 /* juce_graphics */ /* juce_graphics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_graphics; path = ../../../../modules/juce_graphics; sourceTree = SOURCE_ROOT; };
		2030A589A9355FE6A0F72428 /* Cocoa.framework */ /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		2A163F48282EEE95B8A8BA7A /* juce_gui_extra */ /* juce_gui_extra */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_extra; path = ../../../../modules/juce_gui_extra; sourceTree = SOURCE_ROOT; };
		2A889138F8B9285E95BDEEE6 /* juce_audio_utils */ /* juce_audio_utils */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_utils; path = ../../../../modules/juce_audio_utils; sourceTree = SOURCE_ROOT; };
		2C4310E5B49051FC40238E11 /* JuceHeader.h */ /* JuceHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JuceHeader.h; path = ../../JuceLibraryCode/JuceHeader.h; sourceTree = SOURCE_ROOT; };
		302A999B2803C0D5C15D237C /* include_juce_data_structures.mm */ /* include_juce_data_structures.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_data_structures.mm; path = ../../JuceLibraryCode/include_juce_data_structures.mm; sourceTree = SOURCE_ROOT; };
		31323D62C5754F4248607F0B /* juce_audio_basics */ /* juce_audio_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_basics; path = ../../../../modules/juce_audio_basics; sourceTree = SOURCE_ROOT; };
		324682B2C8B9B4ACD9711A7A /* include_juce_analytics.cpp */ /* include_juce_analytics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_analytics.cpp; path = ../../JuceLibraryCode/include_juce_analytics.cpp; sourceTree = SOURCE_ROOT; };
		336A244E3C6460495F0A424C /* CoreAudioKit.framework */ /* CoreAudioKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioKit.framework; path = System/Library/Frameworks/CoreAudioKit.framework; sourceTree = SDKROOT; };
		39F1D4C0BF563E43EA8A98B2 /* juce_audio_devices */ /* juce_audio_devices */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_devices; path = ../../../../modules/juce_audio_devices; sourceTree = SOURCE_ROOT; };
		3A26A3568F2C301EEED25288 /* include_juce_audio_processors.mm */ /* include_juce_audio_processors.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_processors.mm; path = ../../JuceLibraryCode/include_juce_audio_processors.mm; sourceTree = SOURCE_ROOT; };
		3D169C5EFBF6304F5CE4C35E /* include_juce_events.mm */ /* include_juce_events.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_events.mm; path = ../../JuceLibraryCode/include_juce_events.mm; sourceTree = SOURCE_ROOT; };
		4195CB317C364D778AE2ADB1 /* include_juce_gui_extra.mm */ /* include_juce_gui_extra.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_extra.mm; path = ../../JuceLibraryCode/include_juce_gui_extra.mm; sourceTree = SOURCE_ROOT; };
		45FB94C047D1ECAACA9462B7 /* include_juce_audio_processors_lv2_libs.cpp */ /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_lv2_libs.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_lv2_libs.cpp; sourceTree = SOURCE_ROOT; };
		46D161431FC09F290F176D30 /* include_juce_javascript.cpp */ /* include_juce_javascript.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_javascript.cpp; path = ../../JuceLibraryCode/include_juce_javascript.cpp; sourceTree = SOURCE_ROOT; };
		4BD792956FE7C22CB8FB691D /* include_juce_audio_basics.mm */ /* include_juce_audio_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_basics.mm; path = ../../JuceLibraryCode/include_juce_audio_basics.mm; sourceTree = SOURCE_ROOT; };
		4CA19EC18C2BC536B3636842 /* include_juce_dsp.mm */ /* include_juce_dsp.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_dsp.mm; path = ../../JuceLibraryCode/include_juce_dsp.mm; sourceTree = SOURCE_ROOT; };
		583EA0E5C4B75A629AEF1157 /* include_juce_gui_basics.mm */ /* include_juce_gui_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_basics.mm; path = ../../JuceLibraryCode/include_juce_gui_basics.mm; sourceTree = SOURCE_ROOT; };
		5B38ECC1B68681432732938C /* Metal.framework */ /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		5C7BDD8DF72F2FC2D44D757A /* RecentFilesMenuTemplate.nib */ /* RecentFilesMenuTemplate.nib */ = {isa = PBXFileReference; lastKnownFileType = file.nib; name = RecentFilesMenuTemplate.nib; path = RecentFilesMenuTemplate.nib; sourceTree = SOURCE_ROOT; };
		5CF6DD6C5477309A1E9AB644 /* Security.framework */ /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		73E9BD8E156D7293C10C298B /* MetalKit.framework */ /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		748F996DD2778AD1442AECA6 /* juce_product_unlocking */ /* juce_product_unlocking */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_product_unlocking; path = ../../../../modules/juce_product_unlocking; sourceTree = SOURCE_ROOT; };
		7898C73DCA6FA9D9CF669D32 /* AudioToolbox.framework */ /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		7C4E4601FFB642386AD27B07 /* juce_events */ /* juce_events */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_events; path = ../../../../modules/juce_events; sourceTree = SOURCE_ROOT; };
		8048091E98040AC90FF4715D /* include_juce_core_CompilationTime.cpp */ /* include_juce_core_CompilationTime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_core_CompilationTime.cpp; path = ../../JuceLibraryCode/include_juce_core_CompilationTime.cpp; sourceTree = SOURCE_ROOT; };
		8165CEA1A009721D3D05D98F /* juce_core */ /* juce_core */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_core; path = ../../../../modules/juce_core; sourceTree = SOURCE_ROOT; };
		846E187EC2E797B982861CA4 /* include_juce_audio_utils.mm */ /* include_juce_audio_utils.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_utils.mm; path = ../../JuceLibraryCode/include_juce_audio_utils.mm; sourceTree = SOURCE_ROOT; };
		88AA2B9840A6792BBAD559EE /* Main.cpp */ /* Main.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Main.cpp; path = ../../Source/Main.cpp; sourceTree = SOURCE_ROOT; };
		8C449538B266A891147103D6 /* IOKit.framework */ /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		8EBA9CF0874619A8FA0B4E74 /* juce_osc */ /* juce_osc */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_osc; path = ../../../../modules/juce_osc; sourceTree = SOURCE_ROOT; };
		8EC828FBFEC92A64A135467C /* juce_midi_ci */ /* juce_midi_ci */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_midi_ci; path = ../../../../modules/juce_midi_ci; sourceTree = SOURCE_ROOT; };
		A40A2A0B2841A622C53047CD /* juce_audio_processors */ /* juce_audio_processors */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_processors; path = ../../../../modules/juce_audio_processors; sourceTree = SOURCE_ROOT; };
		A59D9064C3A2D7EC3DC45420 /* include_juce_osc.cpp */ /* include_juce_osc.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_osc.cpp; path = ../../JuceLibraryCode/include_juce_osc.cpp; sourceTree = SOURCE_ROOT; };
		A76DD7182C290A9020C96CA7 /* include_juce_audio_formats.mm */ /* include_juce_audio_formats.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_formats.mm; path = ../../JuceLibraryCode/include_juce_audio_formats.mm; sourceTree = SOURCE_ROOT; };
		AB19DDC8458D2A420E6D8AC3 /* include_juce_core.mm */ /* include_juce_core.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_core.mm; path = ../../JuceLibraryCode/include_juce_core.mm; sourceTree = SOURCE_ROOT; };
		B38A1AC42B002115350C0268 /* Accelerate.framework */ /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		B4202EE1243A8FCA29996D05 /* DiscRecording.framework */ /* DiscRecording.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DiscRecording.framework; path = System/Library/Frameworks/DiscRecording.framework; sourceTree = SDKROOT; };
		B96EC82EC3D2813B50386198 /* include_juce_product_unlocking.mm */ /* include_juce_product_unlocking.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_product_unlocking.mm; path = ../../JuceLibraryCode/include_juce_product_unlocking.mm; sourceTree = SOURCE_ROOT; };
		C0531453A002C480280C5F05 /* CoreAudio.framework */ /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		C7F2704A6676859CCE14A4D1 /* include_juce_audio_processors_ara.cpp */ /* include_juce_audio_processors_ara.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_ara.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_ara.cpp; sourceTree = SOURCE_ROOT; };
		CC27F53A76BFB2675D2683A1 /* juce_opengl */ /* juce_opengl */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_opengl; path = ../../../../modules/juce_opengl; sourceTree = SOURCE_ROOT; };
		D2EBC6292AE5AFC46EB10DAC /* WebKit.framework */ /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		D3BE73543708D756BDB8AEF7 /* juce_dsp */ /* juce_dsp */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_dsp; path = ../../../../modules/juce_dsp; sourceTree = SOURCE_ROOT; };
		D484C08AF83E4D2DFA030F1A /* Foundation.framework */ /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		D6F54293A21405C783852645 /* juce_audio_formats */ /* juce_audio_formats */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_formats; path = ../../../../modules/juce_audio_formats; sourceTree = SOURCE_ROOT; };
		D782494D4E433C87913F162D /* juce_analytics */ /* juce_analytics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_analytics; path = ../../../../modules/juce_analytics; sourceTree = SOURCE_ROOT; };
		D785E7E0304F3CC1E42A3ADF /* juce_data_structures */ /* juce_data_structures */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_data_structures; path = ../../../../modules/juce_data_structures; sourceTree = SOURCE_ROOT; };
		DA5B8888560BA7B144F88DA5 /* include_juce_graphics_Harfbuzz.cpp */ /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_graphics_Harfbuzz.cpp; path = ../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp; sourceTree = SOURCE_ROOT; };
		DD849A04E38279B842EDE213 /* juce_gui_basics */ /* juce_gui_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_basics; path = ../../../../modules/juce_gui_basics; sourceTree = SOURCE_ROOT; };
		EECBAA403D2D6AEEA8CB05EB /* include_juce_graphics.mm */ /* include_juce_graphics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_graphics.mm; path = ../../JuceLibraryCode/include_juce_graphics.mm; sourceTree = SOURCE_ROOT; };
		F260758DB97CF0F5C85218C1 /* QuartzCore.framework */ /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		FCB76958E12B2D7F8277CD59 /* OpenGL.framework */ /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		136E27FD16209F9868093A6C = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A70F7F4891DB1CF67653BE74,
				96EFF7BA261F57DD829324D8,
				AA207299991F85938465BF65,
				66FC7F44EEC9044E5C4A21C3,
				3866839F4051D104244870B1,
				8D51903C59161885903F60CC,
				32010EA67EEFE024B9CE1CB5,
				EB8BBB7D2DBDB1092CE236E9,
				263250D6F359CE403B0566FF,
				17A09B4AF453B148CD7349F4,
				1A038A2954FB9A4F208BE3F2,
				2AD7D2E1785FCABA09AE3764,
				4BC57B0D2215621D90C8881C,
				26064360DE64348CC75E6340,
				9E31DAE99C88F8975034EA52,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2055BAFF80DBBC11CF2907C0 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B38A1AC42B002115350C0268,
				7898C73DCA6FA9D9CF669D32,
				2030A589A9355FE6A0F72428,
				C0531453A002C480280C5F05,
				336A244E3C6460495F0A424C,
				04C1B8BF62AA09E62B362913,
				B4202EE1243A8FCA29996D05,
				D484C08AF83E4D2DFA030F1A,
				8C449538B266A891147103D6,
				FCB76958E12B2D7F8277CD59,
				F260758DB97CF0F5C85218C1,
				5CF6DD6C5477309A1E9AB644,
				D2EBC6292AE5AFC46EB10DAC,
				5B38ECC1B68681432732938C,
				73E9BD8E156D7293C10C298B,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3F7D4D52FAA229344338F40C /* JUCE Library Code */ = {
			isa = PBXGroup;
			children = (
				324682B2C8B9B4ACD9711A7A,
				4BD792956FE7C22CB8FB691D,
				00CDB93410EA5AECBA5ADA95,
				A76DD7182C290A9020C96CA7,
				3A26A3568F2C301EEED25288,
				C7F2704A6676859CCE14A4D1,
				45FB94C047D1ECAACA9462B7,
				846E187EC2E797B982861CA4,
				AB19DDC8458D2A420E6D8AC3,
				8048091E98040AC90FF4715D,
				08ED235CBE02E0FB4BE4653E,
				302A999B2803C0D5C15D237C,
				4CA19EC18C2BC536B3636842,
				3D169C5EFBF6304F5CE4C35E,
				EECBAA403D2D6AEEA8CB05EB,
				DA5B8888560BA7B144F88DA5,
				0E5DB2C17DAC6067A0DAC84E,
				583EA0E5C4B75A629AEF1157,
				4195CB317C364D778AE2ADB1,
				46D161431FC09F290F176D30,
				0EFA505235D959565503D537,
				1CA82C74AEC08421812BDCAC,
				A59D9064C3A2D7EC3DC45420,
				B96EC82EC3D2813B50386198,
				2C4310E5B49051FC40238E11,
			);
			name = "JUCE Library Code";
			sourceTree = "<group>";
		};
		6D697538ADDCFBB6C79AC6C6 /* Products */ = {
			isa = PBXGroup;
			children = (
				080EAB9CF5AB2BD6B2BBB173,
			);
			name = Products;
			sourceTree = "<group>";
		};
		88C22F995571E4A515721154 /* UnitTestRunner */ = {
			isa = PBXGroup;
			children = (
				9E7E99EBD6772DAF4725435D,
			);
			name = UnitTestRunner;
			sourceTree = "<group>";
		};
		99F3717D3FEAFDCA3C22E868 /* Source */ = {
			isa = PBXGroup;
			children = (
				88C22F995571E4A515721154,
				FE614235C8CE15EFCEB61487,
				3F7D4D52FAA229344338F40C,
				F31A71A7A7566E4D44B2844C,
				2055BAFF80DBBC11CF2907C0,
				6D697538ADDCFBB6C79AC6C6,
			);
			name = Source;
			sourceTree = "<group>";
		};
		9E7E99EBD6772DAF4725435D /* Source */ = {
			isa = PBXGroup;
			children = (
				88AA2B9840A6792BBAD559EE,
			);
			name = Source;
			sourceTree = "<group>";
		};
		F31A71A7A7566E4D44B2844C /* Resources */ = {
			isa = PBXGroup;
			children = (
				5C7BDD8DF72F2FC2D44D757A,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		FE614235C8CE15EFCEB61487 /* JUCE Modules */ = {
			isa = PBXGroup;
			children = (
				D782494D4E433C87913F162D,
				31323D62C5754F4248607F0B,
				39F1D4C0BF563E43EA8A98B2,
				D6F54293A21405C783852645,
				A40A2A0B2841A622C53047CD,
				2A889138F8B9285E95BDEEE6,
				8165CEA1A009721D3D05D98F,
				05501801BF6C4A47598C59E2,
				D785E7E0304F3CC1E42A3ADF,
				D3BE73543708D756BDB8AEF7,
				7C4E4601FFB642386AD27B07,
				1DC921E6494548F5E73E1056,
				DD849A04E38279B842EDE213,
				2A163F48282EEE95B8A8BA7A,
				02D7ADAB1962C77DC0BDCC0E,
				8EC828FBFEC92A64A135467C,
				CC27F53A76BFB2675D2683A1,
				8EBA9CF0874619A8FA0B4E74,
				748F996DD2778AD1442AECA6,
			);
			name = "JUCE Modules";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B0D62ED1051EE9DFD49F6321 /* UnitTestRunner - ConsoleApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FCC937D27051A7D36CD9198E;
			buildPhases = (
				DD4C62A6EA1FD3497B42CDB0,
				E8585DDC9F9A0EE2FB82FA52,
				136E27FD16209F9868093A6C,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "UnitTestRunner - ConsoleApp";
			productName = UnitTestRunner;
			productReference = 080EAB9CF5AB2BD6B2BBB173;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E1E93F2B4B2D17E011395520 = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = "Raw Material Software Limited";
			};
			buildConfigurationList = 18FC121B1014F7999CD135D3;
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 99F3717D3FEAFDCA3C22E868;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B0D62ED1051EE9DFD49F6321,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		DD4C62A6EA1FD3497B42CDB0 = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				5FE50792EDC7638DE9A824B5,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E8585DDC9F9A0EE2FB82FA52 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D17BAE3D36BB94FC2C8E2438,
				0DA13944CF8AD7F862DF03E3,
				BFED026CA071070CEB87CFB5,
				33D24B475EA928745A87EDDB,
				FDDF955477BE7FEBC364E19B,
				74EC8AEC296DB2721EB438BF,
				44628795CE784DEE1B98F986,
				79FE3F2D2EFAC333283E5D90,
				3822F598DA7044E5DB7633A9,
				9B48039CDFD679AD944BAC70,
				2DC417FBA2F857098B3502B2,
				FC139F56BD13A2C78D21076E,
				5CB3596030B0DD3763CAF85C,
				FD15EF066F019B57F0601D71,
				A4C15593F5B4E1BE338F4F63,
				A1A39E64F9E03EFFA10B0A10,
				36F598D91354C54D8B028843,
				69EB54A3097C15333ECB957A,
				D43289CF624A7B068237C192,
				AF1FE82A4A20DCB8944B35C7,
				8931A330E8D2263E787BDBA6,
				B407D123F08A9A8C12624ABA,
				1D06F1A254F84A7AE3E90DF2,
				7164274FE42C7EC423455E05,
				1B09834E81EAF5BCB87FAAF4,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		53146F81C8222491DC6DF2A5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "UnitTestRunner";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Debug;
		};
		962CC7E0A536C3F56DBE1F8F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_cryptography=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_MODULE_AVAILABLE_juce_product_unlocking=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_LV2=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCE_UNIT_TESTS=1",
					"JUCE_SILENCE_XCODE_15_LINKER_WARNING=1",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INSTALL_PATH = "/usr/bin";
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2 $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				OTHER_CFLAGS = "-Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_CPLUSPLUSFLAGS = "-Woverloaded-virtual -Wreorder -Wzero-as-null-pointer-constant -Wunused-private-field -Winconsistent-missing-destructor-override -Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_LDFLAGS = "-Wl,-weak_reference_mismatches,weak";
				PRODUCT_BUNDLE_IDENTIFIER = com.juce.UnitTestRunner;
				PRODUCT_NAME = "UnitTestRunner";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		A5040BB0AC85722F6D54A948 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "UnitTestRunner";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Release;
		};
		A81C9C5D3696F83D5E8CFE11 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_cryptography=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_midi_ci=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_MODULE_AVAILABLE_juce_product_unlocking=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_LV2=1",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCE_UNIT_TESTS=1",
					"JUCE_SILENCE_XCODE_15_LINKER_WARNING=1",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INSTALL_PATH = "/usr/bin";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2 $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				OTHER_CFLAGS = "-Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_CPLUSPLUSFLAGS = "-Woverloaded-virtual -Wreorder -Wzero-as-null-pointer-constant -Wunused-private-field -Winconsistent-missing-destructor-override -Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_LDFLAGS = "-Wl,-weak_reference_mismatches,weak";
				PRODUCT_BUNDLE_IDENTIFIER = com.juce.UnitTestRunner;
				PRODUCT_NAME = "UnitTestRunner";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGL;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		18FC121B1014F7999CD135D3 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				53146F81C8222491DC6DF2A5,
				A5040BB0AC85722F6D54A948,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		FCC937D27051A7D36CD9198E = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A81C9C5D3696F83D5E8CFE11,
				962CC7E0A536C3F56DBE1F8F,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = E1E93F2B4B2D17E011395520 /* Project object */;
}
