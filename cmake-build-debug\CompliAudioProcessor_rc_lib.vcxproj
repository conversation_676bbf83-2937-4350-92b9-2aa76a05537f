﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B4676940-E1CB-3181-A55D-381B2AA508E9}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>CompliAudioProcessor_rc_lib</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">CompliAudioProcessor_rc_lib.dir\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">CompliAudioProcessor_rc_lib.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">CompliAudioProcessor_rc_lib</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">CompliAudioProcessor_rc_lib.dir\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">CompliAudioProcessor_rc_lib.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">CompliAudioProcessor_rc_lib</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">CompliAudioProcessor_rc_lib.dir\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">CompliAudioProcessor_rc_lib.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">CompliAudioProcessor_rc_lib</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">CompliAudioProcessor_rc_lib.dir\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">CompliAudioProcessor_rc_lib.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">CompliAudioProcessor_rc_lib</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ExceptionHandling>
      </ExceptionHandling>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\628366c1adb751f267828c1bc262ae01\CompliAudioProcessor_resources.rc.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
JUCE\tools\extras\Build\juceaide\juceaide_artefacts\Custom\juceaide.exe rcfile C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/Info.txt C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_artefacts\JuceLibraryCode\CompliAudioProcessor_resources.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
JUCE\tools\extras\Build\juceaide\juceaide_artefacts\Custom\juceaide.exe rcfile C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/Info.txt C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_artefacts\JuceLibraryCode\CompliAudioProcessor_resources.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Generating CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
JUCE\tools\extras\Build\juceaide\juceaide_artefacts\Custom\juceaide.exe rcfile C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/Info.txt C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_artefacts\JuceLibraryCode\CompliAudioProcessor_resources.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Generating CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
JUCE\tools\extras\Build\juceaide\juceaide_artefacts\Custom\juceaide.exe rcfile C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/Info.txt C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CompliAudioProcessor_artefacts/JuceLibraryCode/CompliAudioProcessor_resources.rc
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_artefacts\JuceLibraryCode\CompliAudioProcessor_resources.rc</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\GitHub\compli2\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Documents/GitHub/compli2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompilerABI.c;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystem.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckCSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckIncludeFile.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckLibraryExists.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageMessage.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindThreads.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeSystem.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Documents/GitHub/compli2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompilerABI.c;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystem.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckCSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckIncludeFile.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckLibraryExists.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageMessage.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindThreads.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeSystem.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Documents/GitHub/compli2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompilerABI.c;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystem.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckCSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckIncludeFile.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckLibraryExists.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageMessage.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindThreads.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeSystem.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Documents/GitHub/compli2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCCompilerABI.c;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXCompilerABI.cpp;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeCompilerIdDetection.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompileFeatures.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerABI.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineCompilerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeDetermineSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeFindBinUtils.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeParseLibraryArchitecture.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCCompiler.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeRCInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystem.cmake.in;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCXXCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestCompilerCommon.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CMakeTestRCCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckCSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckIncludeFile.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CheckLibraryExists.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\CompilerId\VS-10.vcxproj.in;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageHandleStandardArgs.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindPackageMessage.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\FindThreads.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\CheckSourceCompiles.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Internal\FeatureTesting.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Determine-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-C.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\Windows.cmake;C:\Strawberry\c\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\3.29.2\CMakeSystem.cmake;C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_artefacts\JuceLibraryCode\CompliAudioProcessor_resources.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\ZERO_CHECK.vcxproj">
      <Project>{270C2F31-A56B-3276-942E-4F41F2908F59}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>