// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0D53D8B0AEE37C02C147344B /* IOKit.framework */ = {isa = PBXBuildFile; fileRef = F769CD634476C91F4C9D0596; };
		281394C38788919D523BE021 /* ConsoleApp */ = {isa = PBXBuildFile; fileRef = 799073185F72F5FAD05253C3; };
		3C6FF7689E2FD827A48E2303 /* Main.cpp */ = {isa = PBXBuildFile; fileRef = 50B7C64414A3E778021F5EC4; };
		4B72EFB0E9D74CA7227F6CAB /* Cocoa.framework */ = {isa = PBXBuildFile; fileRef = 57DDB771ED96A256F190ADF8; };
		542006E949BB022F198DF0F2 /* RecentFilesMenuTemplate.nib */ = {isa = PBXBuildFile; fileRef = 1A71A586C0F50B6B328D877B; };
		76329EF5627C14DD1C621F6E /* Security.framework */ = {isa = PBXBuildFile; fileRef = F9E4EBBA2682611A3978340C; };
		9E4D85A3D54739A0FA80A446 /* include_juce_core.mm */ = {isa = PBXBuildFile; fileRef = D186E2D509765FAE0758F17D; };
		C17566E1AACD033B3DD74E9F /* Foundation.framework */ = {isa = PBXBuildFile; fileRef = 6AA23C81496E022290EB2A0C; };
		C72C2005E0871E7542A57DDA /* include_juce_core_CompilationTime.cpp */ = {isa = PBXBuildFile; fileRef = 85EE52F3EA830387CFC8BB96; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A71A586C0F50B6B328D877B /* RecentFilesMenuTemplate.nib */ /* RecentFilesMenuTemplate.nib */ = {isa = PBXFileReference; lastKnownFileType = file.nib; name = RecentFilesMenuTemplate.nib; path = RecentFilesMenuTemplate.nib; sourceTree = SOURCE_ROOT; };
		50B7C64414A3E778021F5EC4 /* Main.cpp */ /* Main.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Main.cpp; path = ../../Source/Main.cpp; sourceTree = SOURCE_ROOT; };
		57DDB771ED96A256F190ADF8 /* Cocoa.framework */ /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = System/Library/Frameworks/Cocoa.framework; sourceTree = SDKROOT; };
		6AA23C81496E022290EB2A0C /* Foundation.framework */ /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		799073185F72F5FAD05253C3 /* ConsoleApp */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = BinaryBuilder; sourceTree = BUILT_PRODUCTS_DIR; };
		85EE52F3EA830387CFC8BB96 /* include_juce_core_CompilationTime.cpp */ /* include_juce_core_CompilationTime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_core_CompilationTime.cpp; path = ../../JuceLibraryCode/include_juce_core_CompilationTime.cpp; sourceTree = SOURCE_ROOT; };
		8702176D2368B6F785546D2B /* juce_core */ /* juce_core */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_core; path = ../../../../modules/juce_core; sourceTree = SOURCE_ROOT; };
		D186E2D509765FAE0758F17D /* include_juce_core.mm */ /* include_juce_core.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_core.mm; path = ../../JuceLibraryCode/include_juce_core.mm; sourceTree = SOURCE_ROOT; };
		D6C3594C8BEC94040AF108FE /* JuceHeader.h */ /* JuceHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JuceHeader.h; path = ../../JuceLibraryCode/JuceHeader.h; sourceTree = SOURCE_ROOT; };
		F769CD634476C91F4C9D0596 /* IOKit.framework */ /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		F9E4EBBA2682611A3978340C /* Security.framework */ /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2722C90F3B5DAD661891FDF4 = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4B72EFB0E9D74CA7227F6CAB,
				C17566E1AACD033B3DD74E9F,
				0D53D8B0AEE37C02C147344B,
				76329EF5627C14DD1C621F6E,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		5D4B80BC67BBCEDCC3B6BAB8 /* JUCE Modules */ = {
			isa = PBXGroup;
			children = (
				8702176D2368B6F785546D2B,
			);
			name = "JUCE Modules";
			sourceTree = "<group>";
		};
		70A875922C34E55D4F48A196 /* Products */ = {
			isa = PBXGroup;
			children = (
				799073185F72F5FAD05253C3,
			);
			name = Products;
			sourceTree = "<group>";
		};
		87ECE820BF4D73DD50D614CB /* Resources */ = {
			isa = PBXGroup;
			children = (
				1A71A586C0F50B6B328D877B,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		8905BD956C24F83087634C3A /* JUCE Library Code */ = {
			isa = PBXGroup;
			children = (
				D186E2D509765FAE0758F17D,
				85EE52F3EA830387CFC8BB96,
				D6C3594C8BEC94040AF108FE,
			);
			name = "JUCE Library Code";
			sourceTree = "<group>";
		};
		9DC13797237D190C48A242C9 /* BinaryBuilder */ = {
			isa = PBXGroup;
			children = (
				AF0451FEC95312712E8145CD,
			);
			name = BinaryBuilder;
			sourceTree = "<group>";
		};
		AF0451FEC95312712E8145CD /* Source */ = {
			isa = PBXGroup;
			children = (
				50B7C64414A3E778021F5EC4,
			);
			name = Source;
			sourceTree = "<group>";
		};
		C18D022743CF5BD14D6A6A9E /* Source */ = {
			isa = PBXGroup;
			children = (
				9DC13797237D190C48A242C9,
				5D4B80BC67BBCEDCC3B6BAB8,
				8905BD956C24F83087634C3A,
				87ECE820BF4D73DD50D614CB,
				E28F51A0038F01E08E22F853,
				70A875922C34E55D4F48A196,
			);
			name = Source;
			sourceTree = "<group>";
		};
		E28F51A0038F01E08E22F853 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				57DDB771ED96A256F190ADF8,
				6AA23C81496E022290EB2A0C,
				F769CD634476C91F4C9D0596,
				F9E4EBBA2682611A3978340C,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		80B70DE094998C267F152DD5 /* BinaryBuilder - ConsoleApp */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A88E30959D785F48A594450E;
			buildPhases = (
				4F5C64675AD3AC67829798FF,
				A69CF4AD8F7015A8D3228FDE,
				2722C90F3B5DAD661891FDF4,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "BinaryBuilder - ConsoleApp";
			productName = BinaryBuilder;
			productReference = 799073185F72F5FAD05253C3;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		36B6F402BC83F21646259DEF = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = "Raw Material Software Limited";
			};
			buildConfigurationList = E4C85B0464A93027D035AA1F;
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C18D022743CF5BD14D6A6A9E;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				80B70DE094998C267F152DD5,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		4F5C64675AD3AC67829798FF = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				542006E949BB022F198DF0F2,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A69CF4AD8F7015A8D3228FDE = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3C6FF7689E2FD827A48E2303,
				9E4D85A3D54739A0FA80A446,
				C72C2005E0871E7542A57DDA,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		00F18709927DE6070FBA7BD0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				EXCLUDED_ARCHS = "";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCE_SILENCE_XCODE_15_LINKER_WARNING=1",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INSTALL_PATH = "/usr/bin";
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				OTHER_LDFLAGS = "-Wl,-weak_reference_mismatches,weak";
				PRODUCT_BUNDLE_IDENTIFIER = com.juce.binarybuilder;
				PRODUCT_NAME = "BinaryBuilder";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Debug;
		};
		7545BE591FD462C37E44B903 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "BinaryBuilder";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Release;
		};
		8A190EF24B99F557190320DA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				EXCLUDED_ARCHS = "";
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCE_SILENCE_XCODE_15_LINKER_WARNING=1",
					"JUCER_XCODE_MAC_F6D2F4CF=1",
					"JUCE_APP_VERSION=1.0.0",
					"JUCE_APP_VERSION_HEX=0x10000",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INSTALL_PATH = "/usr/bin";
				LLVM_LTO = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				OTHER_LDFLAGS = "-Wl,-weak_reference_mismatches,weak";
				PRODUCT_BUNDLE_IDENTIFIER = com.juce.binarybuilder;
				PRODUCT_NAME = "BinaryBuilder";
				USE_HEADERMAP = NO;
				VALID_ARCHS = "i386 x86_64 arm64 arm64e";
			};
			name = Release;
		};
		D9552CAECBA3D7D5725848E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CODE_SIGN_FLAGS = --timestamp;
				PRODUCT_NAME = "BinaryBuilder";
				SDKROOT = macosx;
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A88E30959D785F48A594450E = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00F18709927DE6070FBA7BD0,
				8A190EF24B99F557190320DA,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		E4C85B0464A93027D035AA1F = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7545BE591FD462C37E44B903,
				D9552CAECBA3D7D5725848E5,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 36B6F402BC83F21646259DEF /* Project object */;
}
