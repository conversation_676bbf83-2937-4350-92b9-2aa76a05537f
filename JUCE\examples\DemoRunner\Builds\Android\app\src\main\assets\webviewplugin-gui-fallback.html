<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>WebViewPluginDemo</title>
    <style type="text/css">
      body {
        background-color: white;
      }
    </style>
  </head>
  <body>
    <h1>WebViewPluginDemo</h1>
    <p>
      This document is a placeholder for the GUI component of the
      WebViewPluginDemo.
    </p>
    <p>
      To build the fully fledged user interface you need to install
      <a href="https://nodejs.org">node.js</a>
    </p>
    <p>
      Then navigate into the
      <code>examples/Plugins/WebViewPluginDemoGUI</code> directory inside your JUCE
      directory, and issue the following commands.
    </p>
    <pre>
        npm install
        npm run build
        npm run zip
      </pre
    >
    <p>
      This will build the full GUI package and place it in the
      <code>Assets</code> directory.
    </p>
    <p>After this, rebuild and restart this demo.</p>
  </body>
</html>
