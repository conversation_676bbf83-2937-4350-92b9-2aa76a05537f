^C:\USERS\<USER>\DOCUMENTS\GITHUB\COMPLI2\CMAKELISTS.TXT
setlocal
C:\Strawberry\c\bin\cmake.exe -SC:/Users/<USER>/Documents/GitHub/compli2 -BC:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug --check-stamp-file C:/Users/<USER>/Documents/GitHub/compli2/cmake-build-debug/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
