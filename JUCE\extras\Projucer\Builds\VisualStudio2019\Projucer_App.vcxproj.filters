<?xml version="1.0" encoding="UTF-8"?>

<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Projucer\Application\StartPage">
      <UniqueIdentifier>{1E1D2D75-0ADA-6E9E-105F-2F87632B55AF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Application\Windows">
      <UniqueIdentifier>{DC7E18A5-E854-3D99-627F-AAA88246B712}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Application">
      <UniqueIdentifier>{4F8BCD36-CE20-D951-FB82-2CCEDD0C5898}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\BinaryData\gradle">
      <UniqueIdentifier>{661FA330-2192-FAA3-E7B2-FAF8EBB783C6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\BinaryData\Icons">
      <UniqueIdentifier>{3A77FAA0-7E92-6D59-9C5F-BAAA2BF82833}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\BinaryData\Templates">
      <UniqueIdentifier>{D8DD148A-AB2B-B485-520B-8924DA606099}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\BinaryData">
      <UniqueIdentifier>{FE290DF2-C600-4159-D484-7F48DB498EBE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\CodeEditor">
      <UniqueIdentifier>{DE3E40F0-B495-2AC0-52AF-AA073FFE8E4C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Project\Modules">
      <UniqueIdentifier>{F5C79836-30DE-9DC7-9392-DAAB3F04C18E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Project\UI\Sidebar">
      <UniqueIdentifier>{A0A94AE6-B447-151A-D0DA-FAE9B5410EBF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Project\UI">
      <UniqueIdentifier>{D2E33EF7-EDDC-06BA-9343-EF957E30A158}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Project">
      <UniqueIdentifier>{BBF7BD20-FB7D-59E5-D1DD-3E6F1455CA02}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\ProjectSaving">
      <UniqueIdentifier>{C4676327-23FA-ED8F-1881-BC5E53840936}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Settings">
      <UniqueIdentifier>{FAD9788E-4FE6-172B-0E32-913C0C8FC2FB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Utility\Helpers">
      <UniqueIdentifier>{A353D068-8C43-A573-8460-59B6BA167F83}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Utility\PIPs">
      <UniqueIdentifier>{A4B9C07E-05B1-BCE9-E075-7E573FFD23B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Utility\UI\PropertyComponents">
      <UniqueIdentifier>{ACCBC32A-58D5-1EC6-FC4D-B3B32CB6588E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Utility\UI">
      <UniqueIdentifier>{A90A32B8-1A07-8900-6E90-EC981F56EC9D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer\Utility">
      <UniqueIdentifier>{F77CA057-8DE4-E076-7EB6-D2646794864B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Projucer">
      <UniqueIdentifier>{7DBEF27C-2AFE-DA02-1DBF-E80FAAC99EA7}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_build_tools\utils">
      <UniqueIdentifier>{D9FAFF6D-6737-F775-056A-D0B29BE13820}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_build_tools">
      <UniqueIdentifier>{065C11E4-EB37-5B72-0A01-F549675EB866}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\containers">
      <UniqueIdentifier>{42F7BE9D-3C8A-AE26-289B-8F355C068036}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\detail">
      <UniqueIdentifier>{4C5ED3D6-28D2-8BFF-F891-96201A9DE159}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\files">
      <UniqueIdentifier>{7868764A-6572-381A-906C-9C26792A4C29}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\json">
      <UniqueIdentifier>{ED602AA0-0A43-9721-5882-747B526C812E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\logging">
      <UniqueIdentifier>{07D27C1D-3227-F527-356C-17DA11551A99}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\maths">
      <UniqueIdentifier>{6146D580-99D2-A6C8-5908-30DC355BB6BA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\memory">
      <UniqueIdentifier>{C67003E8-BEA8-2188-F4B3-A122F4B4FA3F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\misc">
      <UniqueIdentifier>{09B91E68-1FF4-C7ED-9055-D4D96E66A0BA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\native\java">
      <UniqueIdentifier>{30B3DA63-C1E4-F2EA-CEF0-8035D8CBFF64}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\native">
      <UniqueIdentifier>{4F24EEED-AA33-AC6C-9A39-72E71CF83EF0}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\network">
      <UniqueIdentifier>{0F70B1A9-BB50-23F5-2AE7-F95E51A00389}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\serialisation">
      <UniqueIdentifier>{D4D9BC01-0DED-2577-4B99-2FF7B9C7EF8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\streams">
      <UniqueIdentifier>{D4C8DC40-2CD2-04B6-05D0-1E7A88841390}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\system">
      <UniqueIdentifier>{58BED6AF-DB89-7560-B2B8-D937C1C0825A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\text">
      <UniqueIdentifier>{B958F86B-6926-8D9B-2FC6-8BFD4BDC72C9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\threads">
      <UniqueIdentifier>{DB624F7D-D513-25AC-C13C-B9062EB3BEEE}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\time">
      <UniqueIdentifier>{89AA9B6C-4029-A34F-C1B0-3B5D8691F4D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\unit_tests">
      <UniqueIdentifier>{1A7F541C-B032-9C66-C320-A13B2A8A9866}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\xml">
      <UniqueIdentifier>{4BAB7C18-51AB-0D9D-83CD-9C37F28D2E38}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\zip\zlib">
      <UniqueIdentifier>{5523922E-8B0C-A52B-477C-752C09F8197F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\zip">
      <UniqueIdentifier>{857B6D8B-0ECB-FE9E-D1EB-D5E45E72F057}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core">
      <UniqueIdentifier>{BAA582FA-40B7-320E-EE7A-4C3892C7BE72}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_cryptography\encryption">
      <UniqueIdentifier>{89B3E447-34BE-C691-638E-09796C6B647E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_cryptography\hashing">
      <UniqueIdentifier>{9BE78436-DBF4-658C-579B-ED19FFD0EB5D}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_cryptography">
      <UniqueIdentifier>{21E7FA61-9E0A-4BA1-04B7-AF47AFA9CB8B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures\app_properties">
      <UniqueIdentifier>{632B4C79-AF7D-BFB5-D006-5AE67F607130}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures\undomanager">
      <UniqueIdentifier>{B10E20C2-4583-2B79-60B7-FE4D4B044313}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures\values">
      <UniqueIdentifier>{CFB54F15-8A8A-0505-9B7F-ECA41CEE38E8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures">
      <UniqueIdentifier>{911F0159-A7A8-4A43-3FD4-154F62F4A44B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\broadcasters">
      <UniqueIdentifier>{9D5816C2-E2B2-2E3F-B095-AC8BD1100D29}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\interprocess">
      <UniqueIdentifier>{3FDCD000-763F-8477-9AF8-70ABA2E91E5E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\messages">
      <UniqueIdentifier>{0947506F-66FA-EF8D-8A4E-4D48BCDBB226}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\native">
      <UniqueIdentifier>{E4B6AED3-F54C-3FF2-069F-640BACAE0E08}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\timers">
      <UniqueIdentifier>{D5EADBCC-6A1C-C940-0206-26E49110AF08}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events">
      <UniqueIdentifier>{D27DC92D-5BEB-9294-DCD1-81D54E245AD5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\colour">
      <UniqueIdentifier>{BCD73D20-42B1-6CDB-DE66-B06236A60F47}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\contexts">
      <UniqueIdentifier>{20DC13F6-2369-8841-9F0B-D13FA14EEE74}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\detail">
      <UniqueIdentifier>{0B30279D-5CEF-3E12-EA90-7D6CE4D52669}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\effects">
      <UniqueIdentifier>{A302A8DB-120F-9EBB-A3D5-2C29963AA56B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT">
      <UniqueIdentifier>{8C9420D9-C764-CBF5-0C95-45A68722E99A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR">
      <UniqueIdentifier>{3EC3A03F-5D43-25E3-C82E-688CDEB00C86}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL">
      <UniqueIdentifier>{462DE0A0-C61F-DAEC-457C-F2C331F6BA13}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix">
      <UniqueIdentifier>{DD7B3E15-B826-9CA6-34E6-AE5187A66799}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\svg">
      <UniqueIdentifier>{924CEEDF-081A-C4B8-B6CB-6579E15F7D71}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color">
      <UniqueIdentifier>{DAD6415C-4D8D-B181-3919-C63E674F1559}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf">
      <UniqueIdentifier>{A43A6434-A029-E35D-3205-06F6644E97E1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common">
      <UniqueIdentifier>{F3EA5987-9668-446D-7994-E5A2307673BC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF">
      <UniqueIdentifier>{F8F5D22C-0EF0-4673-4EA7-D67B35A3EFD3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS">
      <UniqueIdentifier>{6A1D4F6C-32A9-CCE3-AC75-2C4453748E1F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB">
      <UniqueIdentifier>{9F968C68-CB3F-7A0E-8D3F-620C10A2713F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout">
      <UniqueIdentifier>{D9EDBB3F-9EB4-BF65-7117-63C275BFFFE9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\name">
      <UniqueIdentifier>{1CD94C81-9754-F2A6-0E6F-53AD2CA6F254}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC">
      <UniqueIdentifier>{7B438AED-DDA8-FF31-67D3-4ABDF0BF11E4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var">
      <UniqueIdentifier>{C3F2648D-B6F9-6827-36EB-520E888C02D5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT">
      <UniqueIdentifier>{E0C58D4E-D619-E6F8-E0E6-524676456033}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz">
      <UniqueIdentifier>{37EDABAF-7289-F75A-B8D8-FF461ED0AC6B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts">
      <UniqueIdentifier>{45489C2A-6E0E-CCDC-6638-0DACEEB63CCA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\geometry">
      <UniqueIdentifier>{F1B90726-DB55-0293-BFAF-C65C7DF5489C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\image_formats\jpglib">
      <UniqueIdentifier>{2C55FD42-0ACD-B0B8-7EAE-EB17F09BAEEC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\image_formats\pnglib">
      <UniqueIdentifier>{B68CD2B2-701F-9AB7-4638-2485D6E06BCF}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\image_formats">
      <UniqueIdentifier>{B0B7C78E-729E-0FFA-D611-82AE8BC7FE2C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\images">
      <UniqueIdentifier>{0A4F7E12-220C-14EF-0026-9C0629FA9C17}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\native">
      <UniqueIdentifier>{37F49E10-4E62-6D5C-FF70-722D0CA3D97E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\placement">
      <UniqueIdentifier>{160D9882-0F68-278D-C5F9-8960FD7421D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode\sheenbidi\Headers">
      <UniqueIdentifier>{0CB19E25-4E0F-3B63-F005-249DA1602485}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode\sheenbidi\Source">
      <UniqueIdentifier>{26969BB2-447E-3386-5885-61B4896FAA0E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode\sheenbidi">
      <UniqueIdentifier>{5B44DF93-8EE9-BAF5-0B3B-30AE9E58A3F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode">
      <UniqueIdentifier>{A4846E15-C7B2-BB61-80BA-E284529F3AAA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics">
      <UniqueIdentifier>{4CED05DA-E0A2-E548-F753-1F2EF299A8E3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\accessibility\enums">
      <UniqueIdentifier>{46AE69B8-AD58-4381-6CDE-25C8D75B01D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\accessibility\interfaces">
      <UniqueIdentifier>{E56CB4FC-32E8-8740-A3BB-B323CD937A99}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\accessibility">
      <UniqueIdentifier>{4ECDCA0C-BB38-0729-A6B6-2FB0B4D0863B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\application">
      <UniqueIdentifier>{294E4CD5-B06F-97D1-04A3-51871CEA507C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\buttons">
      <UniqueIdentifier>{77228F15-BD91-06FF-2C7E-0377D25C2C94}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\commands">
      <UniqueIdentifier>{5CB531E6-BF9A-2C50-056C-EE5A525D28D3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\components">
      <UniqueIdentifier>{E4EA47E5-B41C-2A19-1783-7E9104096ECD}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\desktop">
      <UniqueIdentifier>{B331BC33-9770-3DB5-73F2-BC2469ECCF7F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\detail">
      <UniqueIdentifier>{3B09E947-B78C-1758-E072-7FD67F8DCB00}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\drawables">
      <UniqueIdentifier>{46A17AC9-0BFF-B5CE-26D6-B9D1992C88AC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\filebrowser">
      <UniqueIdentifier>{D90A8DF7-FBAB-D363-13C0-6707BB22B72B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\keyboard">
      <UniqueIdentifier>{8AE77C40-6839-EC37-4515-BD3CC269BCE4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\layout">
      <UniqueIdentifier>{0EAD99DB-011F-09E5-45A2-365F646EB004}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\lookandfeel">
      <UniqueIdentifier>{F57590C6-3B90-1BE1-1006-488BA33E8BD9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\menus">
      <UniqueIdentifier>{7C319D73-0D93-5842-0874-398D2D3038D5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\misc">
      <UniqueIdentifier>{2CB4DB0C-DD3B-6195-D822-76EC7A5C88D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\mouse">
      <UniqueIdentifier>{FE3CB19C-EF43-5CF5-DAF0-09D4E43D0AB9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\native\accessibility">
      <UniqueIdentifier>{C0E5DD5D-F8F1-DD25-67D7-291946AB3828}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\native">
      <UniqueIdentifier>{895C2D33-E08D-B1BA-BB36-FC4CA65090C8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\positioning">
      <UniqueIdentifier>{D64A57DB-A956-5519-1929-1D929B56E1B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\properties">
      <UniqueIdentifier>{5A99CC24-AC45-7ED6-C11A-B8B86E76D884}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\widgets">
      <UniqueIdentifier>{7A131EEC-25A7-22F6-2839-A2194DDF3007}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\windows">
      <UniqueIdentifier>{EA9DB76C-CEF7-6BFC-2070-28B7DF8E8063}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics">
      <UniqueIdentifier>{3C206A40-6F1B-E683-ACF1-DEC3703D0140}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\code_editor">
      <UniqueIdentifier>{DF95D4BF-E18C-125A-5EBB-8993A06E232C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\detail">
      <UniqueIdentifier>{E0FCBD5F-0B11-D78C-F786-52AB7FEE2383}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\documents">
      <UniqueIdentifier>{118946F2-AC24-0F09-62D5-753DF87A60CD}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\embedding">
      <UniqueIdentifier>{07329F9B-7D3D-CEB3-C771-714842076140}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\misc">
      <UniqueIdentifier>{08BBBECB-B0D1-7611-37EC-F57E1D0CE2A2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\native">
      <UniqueIdentifier>{268E8F2A-980C-BF2F-B161-AACABC9D91F3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra">
      <UniqueIdentifier>{A4D76113-9EDC-DA60-D89B-5BACF7F1C426}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules">
      <UniqueIdentifier>{FE955B6B-68AC-AA07-70D8-2413F6DB65C8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Library Code">
      <UniqueIdentifier>{7ED5A90E-41AF-A1EF-659B-37CEEAB9BA61}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\Source\Application\StartPage\jucer_NewProjectWizard.cpp">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Application\StartPage\jucer_StartPageComponent.cpp">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Application\jucer_Application.cpp">
      <Filter>Projucer\Application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Application\jucer_AutoUpdater.cpp">
      <Filter>Projucer\Application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Application\jucer_CommandLine.cpp">
      <Filter>Projucer\Application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Application\jucer_Main.cpp">
      <Filter>Projucer\Application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Application\jucer_MainWindow.cpp">
      <Filter>Projucer\Application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AnimatedComponentTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AudioComponentTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARADocumentControllerTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARAEditorTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARAPlaybackRendererTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginEditorTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginFilterTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_ComponentTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_ContentCompTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_MainConsoleAppTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_MainTemplate_NoWindow.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_MainTemplate_Window.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_NewComponentTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_NewCppFileTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\Templates\jucer_OpenGLComponentTemplate.cpp">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\CMake\juce_runtime_arch_detection.cpp">
      <Filter>Projucer\BinaryData</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\CMake\juce_LinuxSubprocessHelper.cpp">
      <Filter>Projucer\BinaryData</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\BinaryData\juce_SimpleBinaryBuilder.cpp">
      <Filter>Projucer\BinaryData</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\CodeEditor\jucer_DocumentEditorComponent.cpp">
      <Filter>Projucer\CodeEditor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\CodeEditor\jucer_OpenDocumentManager.cpp">
      <Filter>Projucer\CodeEditor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\CodeEditor\jucer_SourceCodeEditor.cpp">
      <Filter>Projucer\CodeEditor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Project\Modules\jucer_Modules.cpp">
      <Filter>Projucer\Project\Modules</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Project\UI\jucer_HeaderComponent.cpp">
      <Filter>Projucer\Project\UI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Project\UI\jucer_ProjectContentComponent.cpp">
      <Filter>Projucer\Project\UI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Project\jucer_Project.cpp">
      <Filter>Projucer\Project</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\ProjectSaving\jucer_ProjectExporter.cpp">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\ProjectSaving\jucer_ProjectSaver.cpp">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\ProjectSaving\jucer_ResourceFile.cpp">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Settings\jucer_AppearanceSettings.cpp">
      <Filter>Projucer\Settings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Settings\jucer_StoredSettings.cpp">
      <Filter>Projucer\Settings</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\Helpers\jucer_CodeHelpers.cpp">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\Helpers\jucer_FileHelpers.cpp">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\Helpers\jucer_MiscUtilities.cpp">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\Helpers\jucer_NewFileWizard.cpp">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\Helpers\jucer_VersionInfo.cpp">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\PIPs\jucer_PIPGenerator.cpp">
      <Filter>Projucer\Utility\PIPs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\UI\jucer_Icons.cpp">
      <Filter>Projucer\Utility\UI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\UI\jucer_JucerTreeViewBase.cpp">
      <Filter>Projucer\Utility\UI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\UI\jucer_ProjucerLookAndFeel.cpp">
      <Filter>Projucer\Utility\UI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\Source\Utility\UI\jucer_SlidingPanelComponent.cpp">
      <Filter>Projucer\Utility\UI</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_BinaryResourceFile.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_BuildHelperFunctions.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_CppTokeniserFunctions.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_Entitlements.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_Icons.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_PlistOptions.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_ResourceFileHelpers.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_ResourceRc.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\utils\juce_VersionNumbers.cpp">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\Build\juce_build_tools\juce_build_tools.cpp">
      <Filter>JUCE Modules\juce_build_tools</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_AbstractFifo.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_ArrayBase.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_DynamicObject.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_Enumerate_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_FixedSizeFunction_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_HashMap_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_ListenerList_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_NamedValueSet.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_Optional_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_OwnedArray.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_PropertySet.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_ReferenceCountedArray.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_SparseSet.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_Variant.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_common_MimeTypes.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_DirectoryIterator.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_File.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileFilter.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileInputStream.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileOutputStream.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileSearchPath.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_RangedDirectoryIterator.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_TemporaryFile.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_WildcardFileFilter.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\json\juce_JSON.cpp">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\json\juce_JSONSerialisation_test.cpp">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\json\juce_JSONUtils.cpp">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\logging\juce_FileLogger.cpp">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\logging\juce_Logger.cpp">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_BigInteger.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_Expression.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_MathsFunctions_test.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_Random.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\memory\juce_AllocationHooks.cpp">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\memory\juce_MemoryBlock.cpp">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\memory\juce_SharedResourcePointer_test.cpp">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_ConsoleApplication.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_EnumHelpers_test.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_Result.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_RuntimePermissions.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_ScopeGuard.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_Uuid.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_AndroidDocument_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_CommonFile_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_JNIHelpers_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Misc_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_NamedPipe_posix.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_curl.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_ObjCHelpers_mac_test.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_PlatformTimer_generic.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_PlatformTimer_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Process_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Registry_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_RuntimePermissions_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Strings_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_wasm.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_IPAddress.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_MACAddress.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_NamedPipe.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_Socket.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_URL.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_WebInputStream.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_BufferedInputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_FileInputSource.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_InputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_MemoryInputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_MemoryOutputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_OutputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_SubregionStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_URLInputSource.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\system\juce_SystemStats.cpp">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_Base64.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharacterFunctions.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF8_test.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF16_test.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF32_test.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_Identifier.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_LocalisedStrings.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_String.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_StringArray.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_StringPairArray.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_StringPool.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_TextDiff.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_ChildProcess.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_HighResolutionTimer.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_ReadWriteLock.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_Thread.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_ThreadPool.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_TimeSliceThread.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_WaitableEvent.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\time\juce_PerformanceCounter.cpp">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\time\juce_RelativeTime.cpp">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\time\juce_Time.cpp">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\unit_tests\juce_UnitTest.cpp">
      <Filter>JUCE Modules\juce_core\unit_tests</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\xml\juce_XmlDocument.cpp">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\xml\juce_XmlElement.cpp">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\adler32.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\compress.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\crc32.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\deflate.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\infback.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\inffast.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\inflate.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\inftrees.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\trees.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\uncompr.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\zutil.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\juce_GZIPCompressorOutputStream.cpp">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\juce_GZIPDecompressorInputStream.cpp">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\juce_ZipFile.cpp">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\juce_core.cpp">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\juce_core.mm">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\juce_core_CompilationTime.cpp">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\encryption\juce_BlowFish.cpp">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\encryption\juce_Primes.cpp">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\encryption\juce_RSAKey.cpp">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\hashing\juce_MD5.cpp">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\hashing\juce_SHA256.cpp">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\hashing\juce_Whirlpool.cpp">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\juce_cryptography.cpp">
      <Filter>JUCE Modules\juce_cryptography</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\juce_cryptography.mm">
      <Filter>JUCE Modules\juce_cryptography</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_ApplicationProperties.cpp">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_PropertiesFile.cpp">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoableAction.cpp">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoManager.cpp">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_CachedValue.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_Value.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTree.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreePropertyWithDefault_test.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\juce_data_structures.cpp">
      <Filter>JUCE Modules\juce_data_structures</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\juce_data_structures.mm">
      <Filter>JUCE Modules\juce_data_structures</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_ActionBroadcaster.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_AsyncUpdater.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_ChangeBroadcaster.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_LockingAsyncUpdater.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_ChildProcessManager.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_ConnectedChildProcess.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnection.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnectionServer.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_NetworkServiceDiscovery.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_ApplicationBase.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_DeletedAtShutdown.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_MessageListener.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_MessageManager.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_MessageManager_ios.mm">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_MessageManager_mac.mm">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_Messaging_android.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_Messaging_linux.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_Messaging_windows.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_ScopedLowPowerModeDisabler.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_WinRTWrapper_windows.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\timers\juce_MultiTimer.cpp">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\timers\juce_Timer.cpp">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\juce_events.cpp">
      <Filter>JUCE Modules\juce_events</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\juce_events.mm">
      <Filter>JUCE Modules\juce_events</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_Colour.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_ColourGradient.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_Colours.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_FillType.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\contexts\juce_GraphicsContext.cpp">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.cpp">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_JustifiedText.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_Ranges.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_ShapedText.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_SimpleShapedText.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\effects\juce_DropShadowEffect.cpp">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\effects\juce_GlowEffect.cpp">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\VARC.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\failing-alloc.c">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\harfbuzz-subset.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\harfbuzz.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-map.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-blob.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-serialize.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-verify.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-common.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-coretext-font.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-coretext-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-directwrite.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-draw.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face-builder.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-fallback-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-font.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ft.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gdi.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-glib.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gobject-structs.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-graphite2.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-icu.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-map.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-number.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-table.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff2-table.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-color.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-face.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-font.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-map.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-fallback.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-normalize.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-default.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-hangul.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-hebrew.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic-table.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-khmer.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-myanmar.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-syllabic.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-thai.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-vowel-constraints.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-outline.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint-extents.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-static.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-style.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff-common.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff1.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff2.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-input.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-iup.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-solver.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-repacker.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ucd.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-uniscribe.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_AttributedString.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_Font.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_FontOptions.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_GlyphArrangement.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_TextLayout.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_Typeface.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_TypefaceTestData.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_AffineTransform.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_EdgeTable.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_Parallelogram_test.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_Path.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathIterator.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathStrokeType.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_Rectangle_test.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcapimin.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcapistd.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jccoefct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jccolor.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcdctmgr.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jchuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcinit.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcmainct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcmarker.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcmaster.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcomapi.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcparam.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcphuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcprepct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcsample.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jctrans.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdapimin.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdapistd.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdatasrc.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdcoefct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdcolor.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jddctmgr.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdhuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdinput.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmainct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmarker.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmaster.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmerge.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdphuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdpostct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdsample.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdtrans.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jerror.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jfdctflt.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jfdctfst.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jfdctint.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctflt.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctfst.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctint.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctred.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmemmgr.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmemnobs.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jquant1.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jquant2.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jutils.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\transupp.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\png.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngerror.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngget.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngmem.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngpread.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngread.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngrio.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngrtran.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngrutil.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngset.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngtrans.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwio.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwrite.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwtran.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwutil.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\juce_GIFLoader.cpp">
      <Filter>JUCE Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\juce_JPEGLoader.cpp">
      <Filter>JUCE Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\juce_PNGLoader.cpp">
      <Filter>JUCE Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_Image.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_ImageCache.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_ImageConvolutionKernel.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_ImageFileFormat.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_CoreGraphicsContext_mac.mm">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DGraphicsContext_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DHelpers_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DHwndContext_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImage_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImageContext_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DMetrics_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DResources_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_DirectWriteTypeface_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_android.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_freetype.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_linux.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_mac.mm">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_GraphicsContext_android.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_android.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_linux.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_mac.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\placement\juce_RectanglePlacement.cpp">
      <Filter>JUCE Modules\juce_graphics\placement</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiChain.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiTypeLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BracketQueue.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\GeneralCategoryLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\IsolatingRun.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\LevelRun.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\PairingLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunQueue.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBAlgorithm.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBBase.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBCodepointSequence.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLine.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLog.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBMirrorLocator.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBParagraph.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBScriptLocator.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptStack.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SheenBidi.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\StatusStack.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_Unicode.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeBidi.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeGenerated.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeLine.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeScript.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeUtils.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics.cpp">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics.mm">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics_Harfbuzz.cpp">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics_Sheenbidi.c">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\accessibility\juce_AccessibilityHandler.cpp">
      <Filter>JUCE Modules\juce_gui_basics\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\application\juce_Application.cpp">
      <Filter>JUCE Modules\juce_gui_basics\application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ArrowButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_Button.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_DrawableButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_HyperlinkButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ImageButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ShapeButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_TextButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToggleButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToolbarButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_Component.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_ComponentListener.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_FocusTraverser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_ModalComponentManager.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Desktop.cpp">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Displays.cpp">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\detail\juce_AccessibilityHelpers.cpp">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ComponentPeerHelpers.cpp">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_Drawable.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableComposite.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableImage.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawablePath.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableRectangle.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableShape.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableText.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_SVGParser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ContentSharer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileListComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_CaretComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyListener.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyPress.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_ModifierKeys.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_BorderedComponentBoundsConstrainer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentAnimator.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBuilder.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ConcertinaPanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_FlexBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Grid.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GridItem.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GroupComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ScrollBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_SidePanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedButtonBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Viewport.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V4.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_BurgerMenuComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarModel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_PopupMenu.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\misc\juce_BubbleComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\misc\juce_DropShadower.cpp">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\misc\juce_FocusOutline.cpp">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_ComponentDragger.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseCursor.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseEvent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInputSource.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseListener.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityElement_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilitySharedCode_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityTextHelpers_test.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_ContentSharer_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_ContentSharer_ios.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_DragAndDrop_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_DragAndDrop_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_MainMenu_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_MouseCursor_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NSViewComponentPeer_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedDPIAwarenessDisabler.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_UIViewComponentPeer_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_VBlank_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowsHooks_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_XSymbols_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_XWindowSystem_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_MarkerList.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePoint.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePointPath.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeRectangle.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_MultiChoicePropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyPanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_TextPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ComboBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ImageComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Label.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ListBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ProgressBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Slider.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableListBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TextEditor.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TextEditorModel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Toolbar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TreeView.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_AlertWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_CallOutBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ComponentPeer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DialogWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DocumentWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_MessageBoxOptions.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeMessageBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeScaleFactorNotifier.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ResizableWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ScopedMessageBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TooltipWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TopLevelWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_VBlankAttachment.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\juce_gui_basics.cpp">
      <Filter>JUCE Modules\juce_gui_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\juce_gui_basics.mm">
      <Filter>JUCE Modules\juce_gui_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeDocument.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\documents\juce_FileBasedDocument.cpp">
      <Filter>JUCE Modules\juce_gui_extra\documents</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_ColourSelector.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_LiveConstantEditor.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PreferencesPanel.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PushNotifications.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SplashScreen.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebBrowserComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebControlRelays.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_ActiveXComponent_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_AndroidViewComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_AppleRemote_mac.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_HWNDComponent_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_NSViewComponent_mac.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_PushNotifications_android.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_PushNotifications_ios.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_PushNotifications_mac.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_SystemTrayIcon_linux.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_SystemTrayIcon_mac.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_SystemTrayIcon_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_UIViewComponent_ios.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_android.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_linux.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_mac.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_XEmbedComponent_linux.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\juce_gui_extra.cpp">
      <Filter>JUCE Modules\juce_gui_extra</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\juce_gui_extra.mm">
      <Filter>JUCE Modules\juce_gui_extra</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\BinaryData.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_build_tools.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core_CompilationTime.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_cryptography.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_data_structures.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_events.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Harfbuzz.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Sheenbidi.c">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_basics.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_extra.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Source\Application\StartPage\jucer_ContentComponents.h">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\StartPage\jucer_NewProjectTemplates.h">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\StartPage\jucer_NewProjectWizard.h">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\StartPage\jucer_StartPageComponent.h">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\StartPage\jucer_StartPageTreeHolder.h">
      <Filter>Projucer\Application\StartPage</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_AboutWindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_EditorColourSchemeWindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_FloatingToolWindow.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_GlobalPathsWindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_PIPCreatorWindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_SVGPathDataWindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_TranslationToolWindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\Windows\jucer_UTF8WindowComponent.h">
      <Filter>Projucer\Application\Windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_Application.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_AutoUpdater.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_CommandIDs.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_CommandLine.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_CommonHeaders.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_Headers.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Application\jucer_MainWindow.h">
      <Filter>Projucer\Application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AnimatedComponentSimpleTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AnimatedComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioComponentSimpleTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARADocumentControllerTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARAEditorTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARAFilterTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginARAPlaybackRendererTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginEditorTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_AudioPluginFilterTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_ComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_ContentCompSimpleTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_ContentCompTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_InlineComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_NewComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_NewCppFileTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_NewInlineComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_OpenGLComponentSimpleTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_OpenGLComponentTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_PIPAudioProcessorTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\BinaryData\Templates\jucer_PIPTemplate.h">
      <Filter>Projucer\BinaryData\Templates</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\CodeEditor\jucer_DocumentEditorComponent.h">
      <Filter>Projucer\CodeEditor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\CodeEditor\jucer_ItemPreviewComponent.h">
      <Filter>Projucer\CodeEditor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\CodeEditor\jucer_OpenDocumentManager.h">
      <Filter>Projucer\CodeEditor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\CodeEditor\jucer_SourceCodeEditor.h">
      <Filter>Projucer\CodeEditor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\Modules\jucer_AvailableModulesList.h">
      <Filter>Projucer\Project\Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\Modules\jucer_ModuleDescription.h">
      <Filter>Projucer\Project\Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\Modules\jucer_Modules.h">
      <Filter>Projucer\Project\Modules</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\Sidebar\jucer_ExporterTreeItems.h">
      <Filter>Projucer\Project\UI\Sidebar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\Sidebar\jucer_FileTreeItems.h">
      <Filter>Projucer\Project\UI\Sidebar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\Sidebar\jucer_ModuleTreeItems.h">
      <Filter>Projucer\Project\UI\Sidebar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\Sidebar\jucer_ProjectTreeItemBase.h">
      <Filter>Projucer\Project\UI\Sidebar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\Sidebar\jucer_Sidebar.h">
      <Filter>Projucer\Project\UI\Sidebar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\Sidebar\jucer_TreeItemTypes.h">
      <Filter>Projucer\Project\UI\Sidebar</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\jucer_ContentViewComponents.h">
      <Filter>Projucer\Project\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\jucer_FileGroupInformationComponent.h">
      <Filter>Projucer\Project\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\jucer_HeaderComponent.h">
      <Filter>Projucer\Project\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\jucer_ModulesInformationComponent.h">
      <Filter>Projucer\Project\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\jucer_ProjectContentComponent.h">
      <Filter>Projucer\Project\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\UI\jucer_ProjectMessagesComponent.h">
      <Filter>Projucer\Project\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Project\jucer_Project.h">
      <Filter>Projucer\Project</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ProjectExport_Android.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ProjectExport_Make.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ProjectExport_MSVC.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ProjectExport_Xcode.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ProjectExporter.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ProjectSaver.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_ResourceFile.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ProjectSaving\jucer_XcodeProjectParser.h">
      <Filter>Projucer\ProjectSaving</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Settings\jucer_AppearanceSettings.h">
      <Filter>Projucer\Settings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Settings\jucer_StoredSettings.h">
      <Filter>Projucer\Settings</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_CodeHelpers.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_Colours.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_FileHelpers.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_MiscUtilities.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_NewFileWizard.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_PresetIDs.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_TranslationHelpers.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_ValueSourceHelpers.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\Helpers\jucer_VersionInfo.h">
      <Filter>Projucer\Utility\Helpers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\PIPs\jucer_PIPGenerator.h">
      <Filter>Projucer\Utility\PIPs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\PropertyComponents\jucer_ColourPropertyComponent.h">
      <Filter>Projucer\Utility\UI\PropertyComponents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\PropertyComponents\jucer_FilePathPropertyComponent.h">
      <Filter>Projucer\Utility\UI\PropertyComponents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\PropertyComponents\jucer_LabelPropertyComponent.h">
      <Filter>Projucer\Utility\UI\PropertyComponents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\PropertyComponents\jucer_PropertyComponentsWithEnablement.h">
      <Filter>Projucer\Utility\UI\PropertyComponents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\jucer_IconButton.h">
      <Filter>Projucer\Utility\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\jucer_Icons.h">
      <Filter>Projucer\Utility\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\jucer_JucerTreeViewBase.h">
      <Filter>Projucer\Utility\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\jucer_ProjucerLookAndFeel.h">
      <Filter>Projucer\Utility\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\Utility\UI\jucer_SlidingPanelComponent.h">
      <Filter>Projucer\Utility\UI</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_BinaryResourceFile.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_BuildHelperFunctions.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_Entitlements.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_Icons.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_PlistOptions.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_ProjectType.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_RelativePath.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_ResourceFileHelpers.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_ResourceRc.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\utils\juce_VersionNumbers.h">
      <Filter>JUCE Modules\juce_build_tools\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\Build\juce_build_tools\juce_build_tools.h">
      <Filter>JUCE Modules\juce_build_tools</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_AbstractFifo.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Array.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ArrayAllocationBase.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ArrayBase.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_DynamicObject.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ElementComparator.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Enumerate.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_FixedSizeFunction.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_HashMap.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_LinkedListPointer.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ListenerList.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_NamedValueSet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Optional.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_OwnedArray.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_PropertySet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ReferenceCountedArray.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ScopedValueSetter.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_SingleThreadedAbstractFifo.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_SortedSet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Span.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_SparseSet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Variant.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\detail\juce_CallbackListenerList.h">
      <Filter>JUCE Modules\juce_core\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\detail\juce_NativeFileHandle.h">
      <Filter>JUCE Modules\juce_core\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_AndroidDocument.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_common_MimeTypes.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_DirectoryIterator.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_File.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileFilter.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileInputStream.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileOutputStream.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileSearchPath.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_MemoryMappedFile.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_RangedDirectoryIterator.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_TemporaryFile.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_WildcardFileFilter.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\json\juce_JSON.h">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\json\juce_JSONSerialisation.h">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\json\juce_JSONUtils.h">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\logging\juce_FileLogger.h">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\logging\juce_Logger.h">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_BigInteger.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_Expression.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_MathsFunctions.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_NormalisableRange.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_Random.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_Range.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_StatisticsAccumulator.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_AllocationHooks.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Atomic.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ByteOrder.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ContainerDeletePolicy.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_CopyableHeapBlock.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_HeapBlock.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_HeavyweightLeakedObjectDetector.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_LeakedObjectDetector.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Memory.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_MemoryBlock.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_OptionalScopedPointer.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ReferenceCountedObject.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Reservoir.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ScopedPointer.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_SharedResourcePointer.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Singleton.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_WeakReference.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_ConsoleApplication.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_EnumHelpers.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_Functional.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_OptionsHelpers.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_Result.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_RuntimePermissions.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_ScopeGuard.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_Uuid.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_WindowsRegistry.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_BasicNativeHeaders.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_CFHelpers_mac.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_ComSmartPtr_windows.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_IPAddress_posix.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_JNIHelpers_android.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_ObjCHelpers_mac.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_PlatformTimerListener.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_SharedCode_intel.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_SharedCode_posix.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_ThreadPriorities_native.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_IPAddress.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_MACAddress.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_NamedPipe.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_Socket.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_URL.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_WebInputStream.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\serialisation\juce_Serialisation.h">
      <Filter>JUCE Modules\juce_core\serialisation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_AndroidDocumentInputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_BufferedInputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_FileInputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_InputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_InputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_MemoryInputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_MemoryOutputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_OutputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_SubregionStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_URLInputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_CompilerSupport.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_CompilerWarnings.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_PlatformDefs.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_StandardHeader.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_SystemStats.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_TargetPlatform.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_Base64.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharacterFunctions.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_ASCII.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF8.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF16.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF32.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_Identifier.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_LocalisedStrings.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_NewLine.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_String.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringArray.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringPairArray.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringPool.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringRef.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_TextDiff.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ChildProcess.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_CriticalSection.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_DynamicLibrary.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_HighResolutionTimer.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_InterProcessLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_Process.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ReadWriteLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ScopedLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ScopedReadLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ScopedWriteLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_SpinLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_Thread.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ThreadLocalValue.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ThreadPool.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_TimeSliceThread.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_WaitableEvent.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\time\juce_PerformanceCounter.h">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\time\juce_RelativeTime.h">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\time\juce_Time.h">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\unit_tests\juce_UnitTest.h">
      <Filter>JUCE Modules\juce_core\unit_tests</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\unit_tests\juce_UnitTestCategories.h">
      <Filter>JUCE Modules\juce_core\unit_tests</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\xml\juce_XmlDocument.h">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\xml\juce_XmlElement.h">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\crc32.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\deflate.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\gzguts.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inffast.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inffixed.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inflate.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inftrees.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\trees.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\zconf.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\zlib.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\zutil.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_GZIPCompressorOutputStream.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_GZIPDecompressorInputStream.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_ZipFile.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_zlib.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\juce_core.h">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\encryption\juce_BlowFish.h">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\encryption\juce_Primes.h">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\encryption\juce_RSAKey.h">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\hashing\juce_MD5.h">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\hashing\juce_SHA256.h">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\hashing\juce_Whirlpool.h">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\juce_cryptography.h">
      <Filter>JUCE Modules\juce_cryptography</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_ApplicationProperties.h">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_PropertiesFile.h">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoableAction.h">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoManager.h">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_CachedValue.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_Value.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTree.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreePropertyWithDefault.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\juce_data_structures.h">
      <Filter>JUCE Modules\juce_data_structures</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ActionBroadcaster.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ActionListener.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_AsyncUpdater.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ChangeBroadcaster.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ChangeListener.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_LockingAsyncUpdater.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_ChildProcessManager.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_ConnectedChildProcess.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnection.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnectionServer.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_NetworkServiceDiscovery.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_ApplicationBase.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_CallbackMessage.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_DeletedAtShutdown.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_Initialisation.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_Message.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_MessageListener.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_MessageManager.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_MountedVolumeListChangeDetector.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_NotificationType.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_EventLoop_linux.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_EventLoopInternal_linux.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_HiddenMessageWindow_windows.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_MessageQueue_mac.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_RunningInUnity.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_ScopedLowPowerModeDisabler.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_WinRTWrapper_windows.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\timers\juce_MultiTimer.h">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\timers\juce_TimedCallback.h">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\timers\juce_Timer.h">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\juce_events.h">
      <Filter>JUCE Modules\juce_events</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_Colour.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_ColourGradient.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_Colours.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_FillType.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_PixelFormats.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\contexts\juce_GraphicsContext.h">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\contexts\juce_LowLevelGraphicsContext.h">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.h">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_JustifiedText.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_Ranges.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_ShapedText.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_SimpleShapedText.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_Unicode.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\effects\juce_DropShadowEffect.h">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\effects\juce_GlowEffect.h">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\effects\juce_ImageEffectFilter.h">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT\CBDT.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR\COLR.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR\colrv1-closure.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL\CPAL.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix\sbix.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\svg\svg.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\svg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\composite-iter.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\CompositeGlyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\glyf-helpers.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\glyf.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\Glyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\GlyphHeader.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\loca.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\path-builder.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\SimpleGlyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\SubsetGlyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\Coverage.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\CoverageFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\CoverageFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\RangeRecord.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF\GDEF.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\Anchor.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat3.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorMatrix.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ChainContextPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\Common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ContextPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\CursivePos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\CursivePosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ExtensionPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\GPOS.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\LigatureArray.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkArray.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkBasePos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkBasePosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkLigPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkLigPosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkMarkPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkMarkPosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkRecord.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPosFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairSet.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairValueRecord.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PosLookup.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PosLookupSubTable.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePosFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ValueFormat.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSet.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ChainContextSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ContextSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ExtensionSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\GSUB.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Ligature.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSet.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\MultipleSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\MultipleSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ReverseChainSingleSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ReverseChainSingleSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Sequence.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubstFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SubstLookup.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SubstLookupSubTable.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\types.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\name\name.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\name</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\coord-setter.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\VARC.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-ankr-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-bsln-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-feat-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-just-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-kerx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-morx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-opbd-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-trak-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-ltag-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-map.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-algs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-array.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-atomic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bimap.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bit-page.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bit-set-invertible.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bit-set.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-blob.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-blob.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-json.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-text-glyphs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-text-unicode.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cache.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-cs-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-dict-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff1-interp-cs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff2-interp-cs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-common.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-config.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-coretext.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cplusplus.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-debug.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-deprecated.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-directwrite.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-dispatch.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-draw.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-draw.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-font.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-font.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ft-colr.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ft.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gdi.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-geometry.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-glib.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gobject-structs.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gobject.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-graphite2.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-icu.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-iter.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-kern.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-limits.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-machinery.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-map.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-map.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-meta.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ms-feature-ranges.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-multimap.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-mutex.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-null.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-number-parser.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-number.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-object.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-open-file.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-open-type.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-std-str.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff2-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cmap-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-color.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-deprecated.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-face-table-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-face.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-font.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-gasp-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-glyf-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-hdmx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-head-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-hhea-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-hmtx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-kern-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-base-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gdef-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gpos-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gsub-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gsubgpos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-jstf-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-map.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-math-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-maxp-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-language-static.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-language.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-os2-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-os2-unicode-ranges.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-macroman.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-table-v2subset.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-fallback.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-normalize.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-fallback.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-joining-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-pua.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-win1256.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-khmer-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-myanmar-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-syllabic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-vowel-constraints.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-stat-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-avar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-cvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-fvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-gvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-hvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-mvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-varc-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-vorg-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-outline.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint-extents.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-pool.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-priority-queue.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-repacker.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-sanitize.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-serialize.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set-digest.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper-impl.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-string-array.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-style.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-accelerator.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-input.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-iup.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-solver.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan-member-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-repacker.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ucd-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode-emoji-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-uniscribe.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-utf.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-vector.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-version.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-blob.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-buffer.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-face.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-font.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-shape.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_AttributedString.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_Font.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_FontOptions.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_FunctionPointerDestructor.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_GlyphArrangement.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_LruCache.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_TextLayout.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_Typeface.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_TypefaceFileCache.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_AffineTransform.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_BorderSize.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_EdgeTable.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Line.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Parallelogram.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Path.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathIterator.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathStrokeType.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Point.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Rectangle.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_RectangleList.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\cderror.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jchuff.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jconfig.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdct.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdhuff.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jerror.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jinclude.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmemsys.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmorecfg.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jpegint.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jpeglib.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jversion.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\transupp.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\png.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngconf.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngdebug.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pnginfo.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngpriv.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngstruct.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_Image.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ImageCache.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ImageConvolutionKernel.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ImageFileFormat.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ScaledImage.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_CoreGraphicsContext_mac.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_CoreGraphicsHelpers_mac.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DGraphicsContext_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DHwndContext_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImage_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImageContext_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DMetrics_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DPixelDataPage_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_DirectX_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_EventTracing.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_RenderingHelpers.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\placement\juce_Justification.h">
      <Filter>JUCE Modules\juce_graphics\placement</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\placement\juce_RectanglePlacement.h">
      <Filter>JUCE Modules\juce_graphics\placement</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBAlgorithm.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBBase.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBBidiType.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBCodepoint.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBCodepointSequence.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBConfig.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBGeneralCategory.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBLine.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBMirrorLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBParagraph.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBRun.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBScript.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBScriptLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SheenBidi.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiChain.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiTypeLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BracketQueue.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BracketType.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\GeneralCategoryLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\IsolatingRun.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\LevelRun.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\PairingLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunExtrema.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunKind.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunQueue.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBAlgorithm.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBAssert.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBBase.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBCodepointSequence.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLine.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLog.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBMirrorLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBParagraph.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBScriptLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptStack.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\StatusStack.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\juce_graphics.h">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityActions.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\enums</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityEvent.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\enums</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityRole.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\enums</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityCellInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityTableInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityTextInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityValueInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\juce_AccessibilityHandler.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\juce_AccessibilityState.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\application\juce_Application.h">
      <Filter>JUCE Modules\juce_gui_basics\application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ArrowButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_Button.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_DrawableButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_HyperlinkButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ImageButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ShapeButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_TextButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToggleButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToolbarButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandID.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_CachedComponentImage.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_Component.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_ComponentListener.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_ComponentTraverser.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_FocusTraverser.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_ModalComponentManager.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Desktop.h">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Displays.h">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_AccessibilityHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_AlertWindowHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ButtonAccessibilityHandler.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ComponentHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ComponentPeerHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_CustomMouseCursorInfo.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_FocusHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_FocusRestorer.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_LookAndFeelHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_MouseInputSourceImpl.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_MouseInputSourceList.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_PointerState.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScalingHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedContentSharerImpl.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedContentSharerInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedMessageBoxImpl.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedMessageBoxInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_StandardCachedComponentImage.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ToolbarItemDragAndDropOverlayComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_TopLevelWindowManager.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ViewportHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_WindowingHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_Drawable.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableComposite.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableImage.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawablePath.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableRectangle.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableShape.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableText.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ContentSharer.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileBrowserListener.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooser.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileListComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FilePreviewComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_CaretComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyListener.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyPress.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_ModifierKeys.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_SystemClipboard.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_TextEditorKeyMapper.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_TextInputTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_AnimatedPosition.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_AnimatedPositionBehaviours.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_BorderedComponentBoundsConstrainer.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentAnimator.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBuilder.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ConcertinaPanel.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_FlexBox.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_FlexItem.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Grid.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GridItem.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GroupComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ScrollBar.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_SidePanel.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedButtonBar.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Viewport.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V4.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_BurgerMenuComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarModel.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_PopupMenu.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\misc\juce_BubbleComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\misc\juce_DropShadower.h">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\misc\juce_FocusOutline.h">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_ComponentDragger.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_DragAndDropTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_FileDragAndDropTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_LassoComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseCursor.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseEvent.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInputSource.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseListener.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_SelectedItemSet.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_TextDragAndDropTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_TooltipClient.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityElement_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityTextHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAExpandCollapseProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAGridItemProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAGridProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAHelpers_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAInvokeProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAProviderBase_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAProviders_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIARangeValueProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIASelectionProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIATextProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAToggleProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIATransformProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAValueProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAWindowProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_WindowsUIAWrapper_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_CGMetalLayerRenderer_mac.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_MultiTouchMapper.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeModalWrapperComponent_ios.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_PerScreenDisplayLinks_mac.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedDPIAwarenessDisabler.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedThreadDPIAwarenessSetter_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedWindowAssociation_linux.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowsHooks_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_XSymbols_linux.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_XWindowSystem_linux.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_MarkerList.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePoint.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePointPath.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeRectangle.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_MultiChoicePropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyPanel.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_TextPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ComboBox.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ImageComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Label.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ListBox.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ProgressBar.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Slider.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableListBox.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TextEditor.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Toolbar.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemFactory.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TreeView.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_AlertWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_CallOutBox.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ComponentPeer.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DialogWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DocumentWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_MessageBoxOptions.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeMessageBox.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeScaleFactorNotifier.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ResizableWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ScopedMessageBox.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TooltipWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TopLevelWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_VBlankAttachment.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_WindowUtils.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\juce_gui_basics.h">
      <Filter>JUCE Modules\juce_gui_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeDocument.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniserFunctions.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\detail\juce_WebControlRelayEvents.h">
      <Filter>JUCE Modules\juce_gui_extra\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\documents\juce_FileBasedDocument.h">
      <Filter>JUCE Modules\juce_gui_extra\documents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_ActiveXControlComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_AndroidViewComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_HWNDComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_NSViewComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_UIViewComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_XEmbedComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_AppleRemote.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_ColourSelector.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_LiveConstantEditor.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PreferencesPanel.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PushNotifications.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SplashScreen.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebBrowserComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebControlParameterIndexReceiver.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebControlRelays.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\native\juce_NSViewFrameWatcher_mac.h">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\juce_gui_extra.h">
      <Filter>JUCE Modules\juce_gui_extra</Filter>
    </ClInclude>
    <ClInclude Include="..\..\JuceLibraryCode\BinaryData.h">
      <Filter>JUCE Library Code</Filter>
    </ClInclude>
    <ClInclude Include="..\..\JuceLibraryCode\JuceHeader.h">
      <Filter>JUCE Library Code</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\Build\CMake\JuceLV2Defines.h.in">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\LaunchScreen.storyboard">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\PIPAudioProcessor.cpp.in">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\PIPAudioProcessorWithARA.cpp.in">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\PIPComponent.cpp.in">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\PIPConsole.cpp.in">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\RecentFilesMenuTemplate.nib">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\Build\CMake\UnityPluginGUIScript.cs.in">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\gradle\gradle-wrapper.jar">
      <Filter>Projucer\BinaryData\gradle</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\gradle\gradlew">
      <Filter>Projucer\BinaryData\gradle</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\gradle\gradlew.bat">
      <Filter>Projucer\BinaryData\gradle</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\gradle\LICENSE">
      <Filter>Projucer\BinaryData\gradle</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\background_logo.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\export_android.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\export_linux.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\export_visualStudio.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\export_xcode.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\juce_icon.png">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_AnimatedApp.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_AudioApp.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_AudioPlugin.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_ConsoleApp.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_DLL.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_GUI.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_Highlight.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_Openfile.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_OpenGL.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\Icons\wizard_StaticLibrary.svg">
      <Filter>Projucer\BinaryData\Icons</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\colourscheme_dark.xml">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\Source\BinaryData\colourscheme_light.xml">
      <Filter>Projucer\BinaryData</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_core\native\java\README.txt">
      <Filter>JUCE Modules\juce_core\native\java</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_core\zip\zlib\JUCE_CHANGES.txt">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\changes to libjpeg for JUCE.txt">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\libpng_readme.txt">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\JUCE_CHANGES.txt">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi</Filter>
    </None>
    <None Include=".\icon.ico">
      <Filter>JUCE Library Code</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\resources.rc">
      <Filter>JUCE Library Code</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>
