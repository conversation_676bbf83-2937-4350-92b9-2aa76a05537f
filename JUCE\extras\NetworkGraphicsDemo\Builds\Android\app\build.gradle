apply plugin: 'com.android.application'

def ndkVersionString = "26.2.11394342"

android {
    compileSdk 34
    ndkVersion ndkVersionString
    namespace "com.juce.networkgraphicsdemo"
    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
            version "3.22.1"
        }
    }
    signingConfigs {
        juceSigning {
            storeFile     file("${System.properties['user.home']}${File.separator}.android${File.separator}debug.keystore")
            storePassword "android"
            keyAlias      "androiddebugkey"
            keyPassword   "android"
            storeType     "jks"
        }
    }

    defaultConfig {
        applicationId "com.juce.networkgraphicsdemo"
        minSdkVersion    24
        targetSdkVersion 34
        externalNativeBuild {
            cmake {
                arguments "-DANDROID_TOOLCHAIN=clang", "-DANDROID_PLATFORM=android-24", "-DANDROID_STL=c++_static", "-DANDROID_CPP_FEATURES=exceptions rtti", "-DANDROID_ARM_MODE=arm", "-DANDROID_ARM_NEON=TRUE", "-DANDROID_WEAK_API_DEFS=ON", "-DCMAKE_CXX_STANDARD=17", "-DCMAKE_CXX_EXTENSIONS=OFF"
            }
        }
    }

    buildTypes {
         debug {
             initWith debug
             debuggable    true
             jniDebuggable true
             signingConfig signingConfigs.juceSigning
         }
         release {
             initWith release
             debuggable    false
             jniDebuggable false
             signingConfig signingConfigs.juceSigning
         }
    }

    flavorDimensions "default"
    productFlavors {
        debug_ {
            ndk {
                abiFilters "armeabi-v7a", "x86", "arm64-v8a", "x86_64"
            }
            externalNativeBuild {
                cmake {
                    cFlags    "-Ofast"
                    cppFlags  "-Ofast"
                    arguments "-DJUCE_BUILD_CONFIGURATION=DEBUG"
                }
            }

            dimension "default"
        }
        release_ {
            externalNativeBuild {
                cmake {
                    cFlags    "-O3"
                    cppFlags  "-O3"
                    arguments "-DJUCE_BUILD_CONFIGURATION=RELEASE"
                }
            }

            dimension "default"
        }
    }

    variantFilter { variant ->
        def names = variant.flavors*.name
        if (names.contains ("debug_")
              && variant.buildType.name != "debug") {
            setIgnore(true)
        }
        if (names.contains ("release_")
              && variant.buildType.name != "release") {
            setIgnore(true)
        }
    }

    sourceSets {
        main.java.srcDirs +=
            ["../../../../../modules/juce_core/native/javacore/init",
             "../../../../../modules/juce_core/native/javacore/app",
             "../../../../../modules/juce_gui_basics/native/javaopt/app"]

        main.res.srcDirs +=
            []
    }

    repositories {
    }

    dependencies {
    }



}

