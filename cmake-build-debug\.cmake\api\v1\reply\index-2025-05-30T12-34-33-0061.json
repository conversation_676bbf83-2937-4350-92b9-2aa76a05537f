{"cmake": {"generator": {"multiConfig": false, "name": "N<PERSON><PERSON>"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-3.31"}, "version": {"isDirty": false, "major": 3, "minor": 31, "patch": 6, "string": "3.31.6", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-5b580e6b81df854bfba2.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-32ced44daabe4608232c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-41c74856afd4864c0b9c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-5a748b94702ff8dfde89.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-32ced44daabe4608232c.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-41c74856afd4864c0b9c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5b580e6b81df854bfba2.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, "toolchains-v1": {"jsonFile": "toolchains-v1-5a748b94702ff8dfde89.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}