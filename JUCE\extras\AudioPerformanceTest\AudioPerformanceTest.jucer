<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT id="AKfc5m" name="AudioPerformanceTest" projectType="guiapp"
              bundleIdentifier="com.juce.AudioPerformanceTest"
              companyName="Raw Material Software Limited"
              companyCopyright="Raw Material Software Limited" useAppConfig="0"
              addUsingNamespaceToJuceHeader="1" jucerFormatVersion="1">
  <MAINGROUP id="b1eVTe" name="AudioPerformanceTest">
    <GROUP id="{AB66118C-9D88-1C3A-D95C-42892D828E4B}" name="Source">
      <FILE id="SqGU9p" name="Main.cpp" compile="1" resource="0" file="Source/Main.cpp"/>
      <FILE id="A0IkQJ" name="MainComponent.h" compile="0" resource="0" file="Source/MainComponent.h"/>
    </GROUP>
  </MAINGROUP>
  <EXPORTFORMATS>
    <XCODE_MAC targetFolder="Builds/MacOSX">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPerformanceTest"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="AudioPerformanceTest"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_MAC>
    <XCODE_IPHONE targetFolder="Builds/iOS">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPerformanceTest"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="AudioPerformanceTest"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_IPHONE>
    <VS2022 targetFolder="Builds/VisualStudio2022">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug"/>
        <CONFIGURATION isDebug="0" name="Release"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
      </MODULEPATHS>
    </VS2022>
    <LINUX_MAKE targetFolder="Builds/LinuxMakefile">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPerformanceTest"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="AudioPerformanceTest"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
      </MODULEPATHS>
    </LINUX_MAKE>
    <ANDROIDSTUDIO androidActivityClass="com.juce.audioperformancetest.AudioPerformanceTest"
                   androidCpp11="1" targetFolder="Builds/Android" extraCompilerFlags="-mfpu=neon -mfloat-abi=hard -ffast-math -funroll-loops --param max-unroll-times=8 -mhard-float -D_NDK_MATH_NO_SOFTFP=1 -DJUCE_DISABLE_ASSERTIONS=1"
                   gradleToolchainVersion="3.6" androidMinimumSDK="24">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPerformanceTest"/>
        <CONFIGURATION name="Release" isDebug="0" optimisation="6"
                       targetName="AudioPerformanceTest"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
      </MODULEPATHS>
    </ANDROIDSTUDIO>
  </EXPORTFORMATS>
  <MODULES>
    <MODULE id="juce_audio_basics" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_devices" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_formats" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_processors" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_utils" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_core" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_data_structures" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_events" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_graphics" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_gui_basics" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_gui_extra" showAllCode="1" useLocalCopy="0"/>
  </MODULES>
  <JUCEOPTIONS/>
  <LIVE_SETTINGS>
    <OSX/>
  </LIVE_SETTINGS>
</JUCERPROJECT>
