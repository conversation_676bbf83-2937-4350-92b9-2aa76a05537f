<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT id="NTe0XB0ij" name="AudioPluginHost" projectType="guiapp" version="1.0.0"
              juceFolder="../../../juce" bundleIdentifier="com.juce.audiopluginhost"
              companyName="Raw Material Software Limited"
              companyCopyright="Raw Material Software Limited"
              useAppConfig="0" addUsingNamespaceToJuceHeader="1" jucerFormatVersion="1">
  <EXPORTFORMATS>
    <XCODE_MAC targetFolder="Builds/MacOSX" smallIcon="c97aUr" bigIcon="c97aUr"
               microphonePermissionNeeded="1" sendAppleEventsPermissionNeeded="1"
               sendAppleEventsPermissionText="This is required for some third-party plug-ins to function correctly."
               customXcodeResourceFolders="../../examples/Assets" applicationCategory="public.app-category.developer-tools"
               extraDefs="JUCE_SILENCE_XCODE_15_LINKER_WARNING=1" extraLinkerFlags="-Wl,-weak_reference_mismatches,weak">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPluginHost" recommendedWarnings="LLVM"/>
        <CONFIGURATION name="Release" isDebug="0" optimisation="2" targetName="AudioPluginHost"
                       recommendedWarnings="LLVM"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_MAC>
    <LINUX_MAKE targetFolder="Builds/LinuxMakefile" smallIcon="c97aUr" bigIcon="c97aUr">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPluginHost"/>
        <CONFIGURATION name="Release" isDebug="0" optimisation="2" targetName="AudioPluginHost"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
      </MODULEPATHS>
    </LINUX_MAKE>
    <VS2019 targetFolder="Builds/VisualStudio2019" smallIcon="c97aUr" bigIcon="c97aUr"
            extraCompilerFlags="/w44265 /w45038 /w44062">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPluginHost"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="AudioPluginHost"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
      </MODULEPATHS>
    </VS2019>
    <VS2022 targetFolder="Builds/VisualStudio2022" smallIcon="c97aUr" bigIcon="c97aUr"
            extraCompilerFlags="/w44265 /w45038 /w44062">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="AudioPluginHost"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="AudioPluginHost"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
      </MODULEPATHS>
    </VS2022>
    <XCODE_IPHONE targetFolder="Builds/iOS" iosScreenOrientation="" iPadScreenOrientation=""
                  iosDeviceFamily="1,2" microphonePermissionNeeded="1" iosBackgroundAudio="1"
                  iosBackgroundBle="1" smallIcon="c97aUr" bigIcon="c97aUr" customXcodeResourceFolders="../../examples/Assets"
                  extraDefs="JUCE_SILENCE_XCODE_15_LINKER_WARNING=1" extraLinkerFlags="-Wl,-weak_reference_mismatches,weak">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" enablePluginBinaryCopyStep="1" isDebug="1" optimisation="1"
                       linkTimeOptimisation="0" targetName="Plugin Host" recommendedWarnings="LLVM"/>
        <CONFIGURATION name="Release" enablePluginBinaryCopyStep="1" isDebug="0" optimisation="3"
                       targetName="Plugin Host" recommendedWarnings="LLVM"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_IPHONE>
    <ANDROIDSTUDIO targetFolder="Builds/Android" androidMinimumSDK="24" androidInternetNeeded="1"
                   microphonePermissionNeeded="1" smallIcon="c97aUr" bigIcon="c97aUr"
                   androidExtraAssetsFolder="../../examples/Assets" androidBluetoothScanNeeded="1"
                   androidBluetoothAdvertiseNeeded="1" androidBluetoothConnectNeeded="1">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" optimisation="1" linkTimeOptimisation="0"
                       targetName="Plugin Host" recommendedWarnings="LLVM"/>
        <CONFIGURATION name="Release" isDebug="0" optimisation="3" linkTimeOptimisation="1"
                       targetName="Plugin Host" recommendedWarnings="LLVM"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_dsp" path="../../modules"/>
      </MODULEPATHS>
    </ANDROIDSTUDIO>
  </EXPORTFORMATS>
  <MAINGROUP id="YdWL7hi7p" name="AudioPluginHost">
    <GROUP id="{CFED5B3D-D1D8-0F3F-4D67-B2A810D057EF}" name="Source">
      <GROUP id="{6F257CD6-CE86-9BBC-54C1-45E43249E414}" name="Plugins">
        <FILE id="rcuPqK" name="ARAPlugin.cpp" compile="1" resource="0" file="Source/Plugins/ARAPlugin.cpp"/>
        <FILE id="gR1tiA" name="ARAPlugin.h" compile="0" resource="0" file="Source/Plugins/ARAPlugin.h"/>
        <FILE id="pov6wS" name="InternalPlugins.cpp" compile="1" resource="0"
              file="Source/Plugins/InternalPlugins.cpp"/>
        <FILE id="MV6AI1" name="InternalPlugins.h" compile="0" resource="0"
              file="Source/Plugins/InternalPlugins.h"/>
        <FILE id="wwL4tB" name="IOConfigurationWindow.cpp" compile="1" resource="0"
              file="Source/Plugins/IOConfigurationWindow.cpp"/>
        <FILE id="wxvaJK" name="IOConfigurationWindow.h" compile="0" resource="0"
              file="Source/Plugins/IOConfigurationWindow.h"/>
        <FILE id="kmUcW8" name="PluginGraph.cpp" compile="1" resource="0" file="Source/Plugins/PluginGraph.cpp"/>
        <FILE id="cbvjhb" name="PluginGraph.h" compile="0" resource="0" file="Source/Plugins/PluginGraph.h"/>
      </GROUP>
      <GROUP id="{D892BFB2-FE85-B70F-10D3-450F407E2B3D}" name="UI">
        <FILE id="wPgLS9" name="GraphEditorPanel.cpp" compile="1" resource="0"
              file="Source/UI/GraphEditorPanel.cpp"/>
        <FILE id="BDMvfT" name="GraphEditorPanel.h" compile="0" resource="0"
              file="Source/UI/GraphEditorPanel.h"/>
        <FILE id="AVGs2y" name="MainHostWindow.cpp" compile="1" resource="0"
              file="Source/UI/MainHostWindow.cpp"/>
        <FILE id="Ia6Vec" name="MainHostWindow.h" compile="0" resource="0"
              file="Source/UI/MainHostWindow.h"/>
        <FILE id="ygZQZ1" name="PluginWindow.h" compile="0" resource="0" file="Source/UI/PluginWindow.h"/>
      </GROUP>
      <FILE id="OmIhwQ" name="HostStartup.cpp" compile="1" resource="0" file="Source/HostStartup.cpp"/>
      <FILE id="c97aUr" name="JUCEAppIcon.png" compile="0" resource="0" file="Source/JUCEAppIcon.png"/>
    </GROUP>
    <GROUP id="{63C773C8-4305-87DD-DB3F-6D1143EFEC61}" name="BinaryData">
      <FILE id="sId0Lj" name="cassette_recorder.wav" compile="0" resource="1"
            file="../../examples/Assets/cassette_recorder.wav"/>
      <FILE id="VVFrYQ" name="cello.wav" compile="0" resource="1" file="../../examples/Assets/cello.wav"/>
      <FILE id="lVsCge" name="guitar_amp.wav" compile="0" resource="1" file="../../examples/Assets/guitar_amp.wav"/>
      <FILE id="Qs6X5l" name="proaudio.path" compile="0" resource="1" file="../../examples/Assets/proaudio.path"/>
      <FILE id="M2ySFI" name="reverb_ir.wav" compile="0" resource="1" file="../../examples/Assets/reverb_ir.wav"/>
      <FILE id="tlFmWJ" name="singing.ogg" compile="0" resource="1" file="../../examples/Assets/singing.ogg"/>
    </GROUP>
  </MAINGROUP>
  <JUCEOPTIONS JUCE_WASAPI="1" JUCE_DIRECTSOUND="1" JUCE_ALSA="1" JUCE_USE_FLAC="0"
               JUCE_USE_OGGVORBIS="1" JUCE_USE_CDBURNER="0" JUCE_USE_CDREADER="0"
               JUCE_USE_CAMERA="0" JUCE_PLUGINHOST_AU="1" JUCE_WEB_BROWSER="0"
               JUCE_PLUGINHOST_VST3="1" JUCE_PLUGINHOST_LADSPA="1" JUCE_PLUGINHOST_LV2="1"/>
  <MODULES>
    <MODULE id="juce_audio_basics" showAllCode="1"/>
    <MODULE id="juce_audio_devices" showAllCode="1"/>
    <MODULE id="juce_audio_formats" showAllCode="1"/>
    <MODULE id="juce_audio_processors" showAllCode="1"/>
    <MODULE id="juce_audio_utils" showAllCode="1"/>
    <MODULE id="juce_core" showAllCode="1"/>
    <MODULE id="juce_cryptography" showAllCode="1"/>
    <MODULE id="juce_data_structures" showAllCode="1"/>
    <MODULE id="juce_dsp" showAllCode="1"/>
    <MODULE id="juce_events" showAllCode="1"/>
    <MODULE id="juce_graphics" showAllCode="1"/>
    <MODULE id="juce_gui_basics" showAllCode="1"/>
    <MODULE id="juce_gui_extra" showAllCode="1"/>
    <MODULE id="juce_opengl" showAllCode="1"/>
  </MODULES>
  <LIVE_SETTINGS>
    <OSX/>
    <WINDOWS/>
    <LINUX/>
  </LIVE_SETTINGS>
</JUCERPROJECT>
