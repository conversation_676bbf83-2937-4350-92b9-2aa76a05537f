#pragma once

#include "PluginProcessor.h"

//==============================================================================
class CompliLookAndFeel : public juce::LookAndFeel_V4
{
public:
    CompliLookAndFeel();

    void drawRotarySlider(juce::Graphics& g, int x, int y, int width, int height,
                         float sliderPos, float rotaryStartAngle, float rotaryEndAngle,
                         juce::Slider& slider) override;

    void drawToggleButton(juce::Graphics& g, juce::ToggleButton& button,
                         bool shouldDrawButtonAsHighlighted, bool shouldDrawButtonAsDown) override;
};

//==============================================================================
class LevelMeter : public juce::Component, public juce::Timer
{
public:
    LevelMeter();
    void paint(juce::Graphics& g) override;
    void setLevel(float newLevel);
    void timerCallback() override;

private:
    float level = 0.0f;
    float displayLevel = 0.0f;
};

//==============================================================================
class CompliAudioProcessorEditor final : public juce::AudioProcessorEditor, public juce::Timer
{
public:
    explicit CompliAudioProcessorEditor (CompliAudioProcessor&);
    ~CompliAudioProcessorEditor() override;

    //==============================================================================
    void paint (juce::Graphics&) override;
    void resized() override;
    void timerCallback() override;

private:
    // This reference is provided as a quick way for your editor to
    // access the processor object that created it.
    CompliAudioProcessor& processorRef;

    // Look and feel
    CompliLookAndFeel compliLookAndFeel;

    // UI Components
    juce::ComboBox presetSelector;
    juce::ComboBox inputSelector;

    // Compressor controls
    juce::Slider thresholdSlider;
    juce::Slider ratioSlider;
    juce::Slider attackSlider;
    juce::Slider releaseSlider;
    juce::Slider makeupGainSlider;

    // Limiter controls
    juce::Slider limiterThresholdSlider;
    juce::ToggleButton limiterToggle;

    // Bypass button
    juce::TextButton bypassButton;

    // Labels
    juce::Label thresholdLabel, ratioLabel, attackLabel, releaseLabel, makeupGainLabel;
    juce::Label limiterThresholdLabel;

    // Level meters
    LevelMeter inputMeter, outputMeter, grMeter;
    juce::Label inputMeterLabel, outputMeterLabel, grMeterLabel;
    juce::Label inputValueLabel, outputValueLabel, grValueLabel;

    // Parameter attachments
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> thresholdAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> ratioAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> attackAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> releaseAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> makeupGainAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::SliderAttachment> limiterThresholdAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> limiterToggleAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ComboBoxAttachment> presetAttachment;
    std::unique_ptr<juce::AudioProcessorValueTreeState::ButtonAttachment> bypassAttachment;

    // Helper methods
    void setupSlider(juce::Slider& slider, const juce::String& parameterID, const juce::String& suffix);
    void setupLabel(juce::Label& label, const juce::String& text);
    void drawSectionBackground(juce::Graphics& g, juce::Rectangle<int> bounds, const juce::String& title);
    void updatePresetInfo();

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (CompliAudioProcessorEditor)
};
