<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT id="gWI5Ir" name="NetworkGraphicsDemo" projectType="guiapp" bundleIdentifier="com.juce.NetworkGraphicsDemo"
              companyName="Raw Material Software Limited"
              companyCopyright="Raw Material Software Limited" useAppConfig="0"
              addUsingNamespaceToJuceHeader="1" jucerFormatVersion="1">
  <MAINGROUP id="OT9rJ2" name="NetworkGraphicsDemo">
    <GROUP id="{48D54E6E-37F4-B20A-E038-C63E4EDFD4D9}" name="Source">
      <FILE id="BfclEZ" name="Demos.h" compile="0" resource="0" file="Source/Demos.h"/>
      <FILE id="xdUc9q" name="Main.cpp" compile="1" resource="0" file="Source/Main.cpp"/>
      <FILE id="Vjuvqu" name="MasterComponent.h" compile="0" resource="0"
            file="Source/MasterComponent.h"/>
      <FILE id="KbZNxO" name="ClientComponent.h" compile="0" resource="0"
            file="Source/ClientComponent.h"/>
      <FILE id="F7A4kl" name="SharedCanvas.h" compile="0" resource="0" file="Source/SharedCanvas.h"/>
    </GROUP>
    <FILE id="Ww6bQw" name="juce_icon.png" compile="0" resource="1" file="Source/juce_icon.png"/>
  </MAINGROUP>
  <EXPORTFORMATS>
    <XCODE_MAC targetFolder="Builds/MacOSX" bigIcon="Ww6bQw" applicationCategory="public.app-category.developer-tools">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="JUCE Network Graphics Demo"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="JUCE Network Graphics Demo"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_MAC>
    <VS2022 targetFolder="Builds/VisualStudio2022">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="JUCE Network Graphics Demo"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="JUCE Network Graphics Demo"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
      </MODULEPATHS>
    </VS2022>
    <XCODE_IPHONE targetFolder="Builds/iOS" bigIcon="Ww6bQw">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="JUCE Network Graphics Demo"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="JUCE Network Graphics Demo"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
      </MODULEPATHS>
    </XCODE_IPHONE>
    <LINUX_MAKE targetFolder="Builds/LinuxMakefile" bigIcon="Ww6bQw">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" targetName="JUCE Network Graphics Demo"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="JUCE Network Graphics Demo"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
      </MODULEPATHS>
    </LINUX_MAKE>
    <ANDROIDSTUDIO androidActivityClass="com.juce.networkgraphicsdemo.JUCENetworkGraphicsDemo"
                   androidCpp11="1" targetFolder="Builds/Android" bigIcon="Ww6bQw"
                   gradleToolchainVersion="3.6">
      <CONFIGURATIONS>
        <CONFIGURATION name="Debug" isDebug="1" optimisation="6" targetName="JUCE Network Graphics Demo"
                       defines="JUCE_DEBUG=0"/>
        <CONFIGURATION name="Release" isDebug="0" targetName="JUCE Network Graphics Demo"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_osc" path="../../modules"/>
        <MODULEPATH id="juce_opengl" path="../../modules"/>
        <MODULEPATH id="juce_gui_extra" path="../../modules"/>
        <MODULEPATH id="juce_gui_basics" path="../../modules"/>
        <MODULEPATH id="juce_graphics" path="../../modules"/>
        <MODULEPATH id="juce_events" path="../../modules"/>
        <MODULEPATH id="juce_data_structures" path="../../modules"/>
        <MODULEPATH id="juce_cryptography" path="../../modules"/>
        <MODULEPATH id="juce_core" path="../../modules"/>
        <MODULEPATH id="juce_audio_utils" path="../../modules"/>
        <MODULEPATH id="juce_audio_processors" path="../../modules"/>
        <MODULEPATH id="juce_audio_formats" path="../../modules"/>
        <MODULEPATH id="juce_audio_devices" path="../../modules"/>
        <MODULEPATH id="juce_audio_basics" path="../../modules"/>
      </MODULEPATHS>
    </ANDROIDSTUDIO>
  </EXPORTFORMATS>
  <MODULES>
    <MODULE id="juce_audio_basics" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_devices" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_formats" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_processors" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_audio_utils" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_core" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_cryptography" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_data_structures" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_events" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_graphics" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_gui_basics" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_gui_extra" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_opengl" showAllCode="1" useLocalCopy="0"/>
    <MODULE id="juce_osc" showAllCode="1" useLocalCopy="0"/>
  </MODULES>
  <JUCEOPTIONS/>
  <LIVE_SETTINGS>
    <OSX/>
  </LIVE_SETTINGS>
</JUCERPROJECT>
