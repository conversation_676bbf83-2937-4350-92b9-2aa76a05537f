name: JUCE Private Build

on:
  workflow_dispatch:
    inputs:
      triggerer:
        required: false
        type: string
        default: ''
        description: The GitHub ID to receive email notifications (leave blank)
      nightly-targets:
        required: false
        type: string
        default: "[]"
        description: A list of nightly build targets in JSON format
      cpp-std:
        required: false
        type: string
        default: ""
        description: The C++ standard to use (optional [20, 23])

env:
  target_url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

run-name: "[${{ inputs.triggerer && inputs.triggerer || github.event.sender.login }}] ${{ github.sha }}"

jobs:
  setup:
    if: ${{ inputs.nightly-targets == '[]' }}
    name: Set pending commit status
    runs-on: ubuntu-latest
    steps:
      - uses: myrotvorets/set-commit-status-action@master
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          sha: ${{ github.sha }}
          status: pending
          context: CI
          targetUrl: ${{ env.target_url }}
  build:
    if: ${{ always() && (needs.setup.result == 'success' || needs.setup.result == 'skipped') }}
    needs: [setup]
    name: .
    # Not having the ability to do a dynamic 'uses' call is a real pain. To
    # test some new CI configuration you must set the branch in both places
    # below.
    uses: juce-framework/JUCE-utils/.github/workflows/main.yml@master
    with:
      juce-utils-branch: master
      nightly-targets: ${{ inputs.nightly-targets }}
      triggerer: ${{ inputs.triggerer && inputs.triggerer || github.event.sender.login }}
      cpp-std: ${{ inputs.cpp-std }}
    secrets: inherit
  deploy:
    if: ${{ contains(fromJSON('["master", "develop"]'), github.ref_name) && inputs.cpp-std == '' && inputs.nightly-targets == '[]' }}
    needs: [build]
    name: Deploy
    uses: juce-framework/JUCE-utils/.github/workflows/deploy.yml@master
    secrets: inherit
  docs:
    if: ${{ contains(fromJSON('["master", "develop"]'), github.ref_name) && inputs.cpp-std == '' && inputs.nightly-targets == '[]' }}
    needs: [deploy]
    name: Docs
    uses: juce-framework/JUCE-utils/.github/workflows/docs.yml@master
    secrets: inherit
  set-commit-status:
    if: ${{ always() && inputs.nightly-targets == '[]' }}
    needs: [setup, build, deploy, docs]
    runs-on: ubuntu-latest
    env:
      result: ${{ contains(needs.*.result, 'cancelled') && 'cancelled' || (contains(needs.*.result, 'failure') && 'failure' || 'success') }}
    steps:
      - uses: myrotvorets/set-commit-status-action@master
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          sha: ${{ github.sha }}
          status: ${{ contains(fromJSON('["cancelled", "failure"]'), env.result) && 'failure' || env.result }}
          context: CI
          description: ${{ env.result }}
          targetUrl: ${{ env.target_url }}
  notify:
    if: ${{ always() && !contains(needs.*.result, 'cancelled') && !startsWith(inputs.triggerer, 'Nightly Build') }}
    needs: [setup, build, deploy, docs]
    name: Notify
    uses: juce-framework/JUCE-utils/.github/workflows/notify.yml@master
    with:
      triggerer: ${{ inputs.triggerer && inputs.triggerer || github.event.sender.login }}
      context: ${{ toJson(needs) }}
    secrets: inherit
