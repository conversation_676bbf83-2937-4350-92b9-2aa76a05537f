<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
  <dict>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app requires audio input. If you do not have an audio interface connected it will use the built-in microphone.</string>
    <key>NSCameraUsageDescription</key>
    <string>This app requires access to the camera to function correctly.</string>
    <key>NSBluetoothAlwaysUsageDescription</key>
    <string>This app requires access to Bluetooth to function correctly.</string>
    <key>NSBluetoothPeripheralUsageDescription</key>
    <string>This app requires access to Bluetooth to function correctly.</string>
    <key>LSRequiresIPhoneOS</key>
    <true/>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <true/>
    <key>UILaunchStoryboardName</key>
    <string>LaunchScreen</string>
    <key>CFBundleExecutable</key>
    <string>${EXECUTABLE_NAME}</string>
    <key>CFBundleIdentifier</key>
    <string>com.rmsl.jucedemorunner</string>
    <key>CFBundleName</key>
    <string>DemoRunner</string>
    <key>CFBundleDisplayName</key>
    <string>DemoRunner</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleShortVersionString</key>
    <string>8.0.7</string>
    <key>CFBundleVersion</key>
    <string>8.0.7</string>
    <key>NSHumanReadableCopyright</key>
    <string>Copyright (c) - Raw Material Software Limited</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>UIFileSharingEnabled</key>
    <true/>
    <key>UISupportsDocumentBrowser</key>
    <true/>
    <key>UIRequiresFullScreen</key>
    <false/>
    <key>UISupportedInterfaceOrientations</key>
    <array>
      <string>UIInterfaceOrientationLandscapeLeft</string>
      <string>UIInterfaceOrientationLandscapeRight</string>
      <string>UIInterfaceOrientationPortrait</string>
      <string>UIInterfaceOrientationPortraitUpsideDown</string>
    </array>
    <key>UIBackgroundModes</key>
    <array/>
  </dict>
</plist>
