/*
  ==============================================================================

  This is an automatically generated GUI class created by the Projucer!

  Be careful when adding custom code to these files, as only the code within
  the "//[xyz]" and "//[/xyz]" sections will be retained when the file is loaded
  and re-saved.

  Created with Projucer version: %%version%%

  ------------------------------------------------------------------------------

  The Projucer is part of the JUCE library.
  Copyright (c) - Raw Material Software Limited.

  ==============================================================================
*/

//[Headers] You can add your own extra header files here...
//[/Headers]

%%include_files_cpp%%

//[MiscUserDefs] You can add your own user definitions and misc code here...
//[/MiscUserDefs]

//==============================================================================
%%class_name%%::%%class_name%% (%%constructor_params%%)
%%initialisers%%{
    //[Constructor_pre] You can add your own custom stuff here..
    //[/Constructor_pre]

    %%constructor%%

    //[Constructor] You can add your own custom stuff here..
    //[/Constructor]
}

%%class_name%%::~%%class_name%%()
{
    //[Destructor_pre]. You can add your own custom destruction code here..
    //[/Destructor_pre]

    %%destructor%%

    //[Destructor]. You can add your own custom destruction code here..
    //[/Destructor]
}

//==============================================================================
%%method_definitions%%

//[MiscUserCode] You can add your own definitions of your custom methods or any other code here...
//[/MiscUserCode]


//==============================================================================
#if 0
/*  -- Projucer information section --

    This is where the Projucer stores the metadata that describe this GUI layout, so
    make changes in here at your peril!

BEGIN_JUCER_METADATA

%%metadata%%
END_JUCER_METADATA
*/
#endif

%%static_member_definitions%%
//[EndFile] You can add extra defines here...
//[/EndFile]
