<?xml version="1.0" encoding="UTF-8"?>

<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="NetworkGraphicsDemo\Source">
      <UniqueIdentifier>{6803489B-B275-0512-8D8B-D30728E17A76}</UniqueIdentifier>
    </Filter>
    <Filter Include="NetworkGraphicsDemo">
      <UniqueIdentifier>{B8E36EB7-A210-7AC5-D731-E337E72C8A1E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\audio_play_head">
      <UniqueIdentifier>{EB58F05A-A968-CEBE-40C4-107CDD8F240F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\buffers">
      <UniqueIdentifier>{5FCF559E-451A-CB1E-B177-A5DC5A0005BB}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\midi\ump">
      <UniqueIdentifier>{05CE33FC-868F-AA1A-12B8-79C98E753648}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\midi">
      <UniqueIdentifier>{D78296AF-218E-B17E-7F8B-9D148601188D}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\mpe">
      <UniqueIdentifier>{B96EBA26-E668-FFAF-FC53-1EC1337DAF5A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\native">
      <UniqueIdentifier>{D8532E5E-469E-5042-EFC8-238241704735}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\sources">
      <UniqueIdentifier>{777B5D1D-9AF0-B22B-8894-034603EE97F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\synthesisers">
      <UniqueIdentifier>{8292766D-2459-2E7E-7615-17216318BA93}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics\utilities">
      <UniqueIdentifier>{9BD56105-DAB4-EBD5-00DD-BD540E98FE88}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_basics">
      <UniqueIdentifier>{10472B2C-9888-D269-F351-0D0AC3BCD16C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\audio_io">
      <UniqueIdentifier>{BF23FC10-1D57-2A9B-706F-6DD8A7B593D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\midi_io\ump">
      <UniqueIdentifier>{386862D5-4DCC-A4B3-5642-60A201E303EF}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\midi_io">
      <UniqueIdentifier>{092EFC17-7C95-7E04-0ACA-0D61A462EE81}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\include\oboe">
      <UniqueIdentifier>{285118C6-8FDA-7DCE-BEF4-FFB2120876C5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\include">
      <UniqueIdentifier>{69ED6B61-9B8D-D47E-E4A6-2E9F9A94A75A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src\aaudio">
      <UniqueIdentifier>{7CDB7CD1-BB96-F593-3C78-1E06182B5839}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src\common">
      <UniqueIdentifier>{B0A708DE-B4CF-196B-14FB-DC8221509B8E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src\fifo">
      <UniqueIdentifier>{34F46ADE-EE31-227A-A69E-7732E70145F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler">
      <UniqueIdentifier>{BB9B3C77-17FB-E994-8B75-88F1727E4655}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph">
      <UniqueIdentifier>{C0971D77-2F14-190A-E2AE-89D6285F4D5A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src\opensles">
      <UniqueIdentifier>{AABEA333-6524-8891-51C7-6DAEB5700628}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe\src">
      <UniqueIdentifier>{F2D29337-983E-BAD7-7B5C-E0AB3D53D404}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native\oboe">
      <UniqueIdentifier>{C674B0FB-1FC0-2986-94B1-083845018994}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\native">
      <UniqueIdentifier>{0AFC1CE8-F6E6-9817-8C21-8432B2A375DA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices\sources">
      <UniqueIdentifier>{0D1AF264-3AC1-78A2-B2A4-AE6171F9194A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_devices">
      <UniqueIdentifier>{9A5DB854-CFFB-5F88-C566-0E10F994DDB3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\deduplication">
      <UniqueIdentifier>{2D16C77A-4E5B-B439-2856-90E03028DA07}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private">
      <UniqueIdentifier>{38A5DDC7-416E-548F-39DA-887875FE6B20}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected">
      <UniqueIdentifier>{980FE2DB-05D3-5FDA-79DA-067A56F5D19D}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include">
      <UniqueIdentifier>{F336DC25-747A-0663-93D6-E3EB9AA0CBF8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\flac\libFLAC">
      <UniqueIdentifier>{7D78546A-80FC-4DCA-00B9-F191F0AB2179}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\flac">
      <UniqueIdentifier>{9EB3EC7F-2AB7-DDAA-3C05-DF382B728D3F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled">
      <UniqueIdentifier>{6B9FBFDC-1D10-6246-356D-00FF4535CECB}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\floor">
      <UniqueIdentifier>{D6FCFC8E-7136-9109-78C0-91A3EB4C443F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\uncoupled">
      <UniqueIdentifier>{EBF18AC1-F0ED-937A-2824-4307CE2ADAF7}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books">
      <UniqueIdentifier>{5A0F7922-2EFB-6465-57E4-A445B804EFB5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes">
      <UniqueIdentifier>{4EC45416-0E7C-7567-6F75-D0C8CEE7DC4F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib">
      <UniqueIdentifier>{C2985031-0496-55B5-41A8-BAB99E53D89D}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7">
      <UniqueIdentifier>{FB4AB426-7009-0036-BB75-E34256AA7C89}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs\oggvorbis">
      <UniqueIdentifier>{E684D858-09E8-0251-8E86-5657129641E1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\codecs">
      <UniqueIdentifier>{1EF1BF17-F941-243A-04D1-EE617D140CBA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\format">
      <UniqueIdentifier>{344DB016-679C-FBD0-3EC6-4570C47522DE}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats\sampler">
      <UniqueIdentifier>{3D9758A0-9359-1710-87C1-05D475C08B17}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_formats">
      <UniqueIdentifier>{E824435F-FC7B-10BE-5D1A-5DACC51A8836}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format">
      <UniqueIdentifier>{86737735-F6BA-F64A-5EC7-5C9F36755F79}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv">
      <UniqueIdentifier>{CDCCDBC1-E1A7-558D-D4AA-B48003178AE3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix">
      <UniqueIdentifier>{6656EC0E-A4C5-985B-242F-01890BDEFB1B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src">
      <UniqueIdentifier>{107F6637-689A-6DAC-1F5D-FD9734F3A0D9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv">
      <UniqueIdentifier>{C60CB2F9-12ED-74A2-C81C-366287805252}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom">
      <UniqueIdentifier>{0BE3157E-54F5-3F72-7023-A62A81D83443}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\buf-size">
      <UniqueIdentifier>{AD43AFB9-8A3D-C470-E098-4ADA2D6B1C07}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core">
      <UniqueIdentifier>{77E6DFCD-32E7-A7E2-75E9-50C49384FEDA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\data-access">
      <UniqueIdentifier>{8449F5A3-222A-3C21-88BD-2ACA69CD290A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\dynmanifest">
      <UniqueIdentifier>{50066622-9190-C54D-FE24-563064A80DB4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event">
      <UniqueIdentifier>{E8F398A4-2CFC-D98B-343B-FB06B6B54063}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\instance-access">
      <UniqueIdentifier>{18BD9026-D673-60FB-C5C0-E4234E9FE71C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log">
      <UniqueIdentifier>{E469C933-C0FE-3A95-168B-234F8B4B620B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\midi">
      <UniqueIdentifier>{375C3EDB-D1F5-AA38-D498-B462B7BDEDE9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\morph">
      <UniqueIdentifier>{2E0391E6-2B82-F704-4B16-9EF63C4E25C1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\options">
      <UniqueIdentifier>{D1C825D2-2980-075A-3EC0-43930977748F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\parameters">
      <UniqueIdentifier>{B350BD14-1FB6-A9A0-4725-75CFEFC2D067}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\patch">
      <UniqueIdentifier>{96D16B7E-5FC5-182E-8734-37C9D27C2299}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-groups">
      <UniqueIdentifier>{56518C02-F710-7D5E-09E6-4B152D5900C7}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-props">
      <UniqueIdentifier>{8D78CFF1-6E9E-3E78-317D-7954EE6482BB}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\presets">
      <UniqueIdentifier>{6B811726-62F3-6E7C-BE8E-493A61CAA9E4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\resize-port">
      <UniqueIdentifier>{C84EE2C8-14A3-D098-62A4-E1C75B7FA13A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\state">
      <UniqueIdentifier>{42DD7AA9-DF7D-D9F9-E50C-69C44211A42B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\time">
      <UniqueIdentifier>{009A44FF-D1C5-47C0-64CC-9122107C73D1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\ui">
      <UniqueIdentifier>{AAEE24C0-066F-8593-70EA-B7AC7553E885}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\units">
      <UniqueIdentifier>{CEC45021-32A4-16BA-8E12-023B029608CD}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\uri-map">
      <UniqueIdentifier>{E5DFE07F-5901-AF5C-7759-84EBF9717E5E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\urid">
      <UniqueIdentifier>{3A189BF7-28D6-C0C4-B831-97AD9E46FE5A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\worker">
      <UniqueIdentifier>{829FC6C3-87E7-0491-B8C3-DC3905FB6039}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2">
      <UniqueIdentifier>{49174595-84D0-A512-B98C-0CFD2D772B8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2">
      <UniqueIdentifier>{E27C67CB-E138-DCCB-110D-623E2C01F9BC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\serd">
      <UniqueIdentifier>{392635C4-010A-C8A2-F46D-1A3628445E1C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src">
      <UniqueIdentifier>{1FF26A52-F9B9-625F-DEE9-8FEE0C02F0F4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd">
      <UniqueIdentifier>{3A5A13A1-B57C-EF05-AC38-53B08A4C8D4A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\sord">
      <UniqueIdentifier>{97983FD5-3F19-2B58-4941-D8FBB6B92BA4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix">
      <UniqueIdentifier>{ADC9725E-0948-5908-13BD-0275DB25325A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src">
      <UniqueIdentifier>{37AD6CD9-9FE5-A457-B1FE-36A85F973502}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord">
      <UniqueIdentifier>{0F3B119C-FE8B-3978-2D80-53248BBBCDEA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sratom\sratom">
      <UniqueIdentifier>{5E836BFC-319A-1CE7-13C9-BD9E87F0A228}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sratom\src">
      <UniqueIdentifier>{5D8E291B-1BB8-3A55-0939-D13A8589C395}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sratom">
      <UniqueIdentifier>{BE3B7D89-2DE8-3CA1-B00E-55821EF3AAAC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\LV2_SDK">
      <UniqueIdentifier>{E0DE9D5D-2792-148A-2CE1-182A90DD5F0E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\pslextensions">
      <UniqueIdentifier>{DC27B453-334E-6965-BAD5-7F88C3E5BA46}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source">
      <UniqueIdentifier>{4DC60E78-BBC0-B540-63A2-37E14ABBEF09}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\thread\include">
      <UniqueIdentifier>{80C72173-A1E1-C3C5-9288-B889CE2EAFEA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\thread\source">
      <UniqueIdentifier>{4138B955-AA0B-FA86-DBF9-404CAFFFA866}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\thread">
      <UniqueIdentifier>{2B4166B8-F470-F07C-4F51-D2DAAAECBB18}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base">
      <UniqueIdentifier>{9C295115-C0CD-3129-1C4D-FB53299B23FB}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base">
      <UniqueIdentifier>{65526A8B-3447-9DF0-FD5D-00D111126027}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui">
      <UniqueIdentifier>{A54A1F5C-F32F-F97B-9E8A-69922B770A54}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst">
      <UniqueIdentifier>{B90A44F3-B62D-B5C0-81A2-683D2650AEE6}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces">
      <UniqueIdentifier>{DAF30656-5915-0E45-C4E4-54439617D525}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples\vst-utilities\moduleinfotool\source">
      <UniqueIdentifier>{600076D4-829D-CE7A-272C-832A4BBC40AB}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples\vst-utilities\moduleinfotool">
      <UniqueIdentifier>{C02D05C7-CD20-9901-2F02-95A9BD7FA797}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples\vst-utilities">
      <UniqueIdentifier>{47771136-6D29-90C7-2C6E-1728E7D1C485}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples">
      <UniqueIdentifier>{3E938566-9812-78C0-9E81-75858F44C51F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common">
      <UniqueIdentifier>{9266EA90-6A0A-5DDB-9CB7-966BEF03BA5C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting">
      <UniqueIdentifier>{9C713CBA-A9E2-5F4E-F83C-2CAB8533913C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo">
      <UniqueIdentifier>{D5B5DC1F-B81B-0449-5E26-15D1367B0C8C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility">
      <UniqueIdentifier>{2741675A-628F-4473-FF8D-45CD2C214CDA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst">
      <UniqueIdentifier>{63571A07-9AA3-5BB0-1103-0B42A2E6BC9E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source">
      <UniqueIdentifier>{314F43F2-BC8F-B464-EAE7-86B9675454E9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk">
      <UniqueIdentifier>{874C5D0C-6D29-68EE-38BB-26200B56BC89}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types\VST3_SDK">
      <UniqueIdentifier>{86BAA7A7-DC50-35B6-910B-932AEAF257F2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\format_types">
      <UniqueIdentifier>{6B7BE34D-1BC1-C7B9-111F-C55CA8250943}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\processors">
      <UniqueIdentifier>{9B6B6D54-D378-80C2-8CC9-D1D8FB44C2A8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\scanning">
      <UniqueIdentifier>{D0584AC3-6837-14F6-90BF-5EA604D1F074}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\utilities\ARA">
      <UniqueIdentifier>{3CB9AC9F-1F99-25B3-8DC1-7DBB67D2E20C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors\utilities">
      <UniqueIdentifier>{794B64EC-B809-32E3-AD00-4EE6A74802CA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_processors">
      <UniqueIdentifier>{67BE498C-9E1F-C73A-B99A-387C034CE680}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_utils\audio_cd">
      <UniqueIdentifier>{1A9C8538-959B-25E3-473D-B462C9A9D458}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_utils\gui">
      <UniqueIdentifier>{AA9F594C-DFAF-C0A7-0CCD-9F90E54D3A01}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_utils\native">
      <UniqueIdentifier>{230BF784-34F4-3BE8-46D4-54E6B67E5E9E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_utils\players">
      <UniqueIdentifier>{39F680F3-5161-4D1C-EAD0-3911ED808874}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_audio_utils">
      <UniqueIdentifier>{3197198B-A978-E330-C7FB-07E5CE8236C7}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\containers">
      <UniqueIdentifier>{42F7BE9D-3C8A-AE26-289B-8F355C068036}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\detail">
      <UniqueIdentifier>{4C5ED3D6-28D2-8BFF-F891-96201A9DE159}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\files">
      <UniqueIdentifier>{7868764A-6572-381A-906C-9C26792A4C29}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\json">
      <UniqueIdentifier>{ED602AA0-0A43-9721-5882-747B526C812E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\logging">
      <UniqueIdentifier>{07D27C1D-3227-F527-356C-17DA11551A99}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\maths">
      <UniqueIdentifier>{6146D580-99D2-A6C8-5908-30DC355BB6BA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\memory">
      <UniqueIdentifier>{C67003E8-BEA8-2188-F4B3-A122F4B4FA3F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\misc">
      <UniqueIdentifier>{09B91E68-1FF4-C7ED-9055-D4D96E66A0BA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\native\java">
      <UniqueIdentifier>{30B3DA63-C1E4-F2EA-CEF0-8035D8CBFF64}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\native">
      <UniqueIdentifier>{4F24EEED-AA33-AC6C-9A39-72E71CF83EF0}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\network">
      <UniqueIdentifier>{0F70B1A9-BB50-23F5-2AE7-F95E51A00389}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\serialisation">
      <UniqueIdentifier>{D4D9BC01-0DED-2577-4B99-2FF7B9C7EF8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\streams">
      <UniqueIdentifier>{D4C8DC40-2CD2-04B6-05D0-1E7A88841390}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\system">
      <UniqueIdentifier>{58BED6AF-DB89-7560-B2B8-D937C1C0825A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\text">
      <UniqueIdentifier>{B958F86B-6926-8D9B-2FC6-8BFD4BDC72C9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\threads">
      <UniqueIdentifier>{DB624F7D-D513-25AC-C13C-B9062EB3BEEE}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\time">
      <UniqueIdentifier>{89AA9B6C-4029-A34F-C1B0-3B5D8691F4D4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\unit_tests">
      <UniqueIdentifier>{1A7F541C-B032-9C66-C320-A13B2A8A9866}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\xml">
      <UniqueIdentifier>{4BAB7C18-51AB-0D9D-83CD-9C37F28D2E38}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\zip\zlib">
      <UniqueIdentifier>{5523922E-8B0C-A52B-477C-752C09F8197F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core\zip">
      <UniqueIdentifier>{857B6D8B-0ECB-FE9E-D1EB-D5E45E72F057}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_core">
      <UniqueIdentifier>{BAA582FA-40B7-320E-EE7A-4C3892C7BE72}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_cryptography\encryption">
      <UniqueIdentifier>{89B3E447-34BE-C691-638E-09796C6B647E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_cryptography\hashing">
      <UniqueIdentifier>{9BE78436-DBF4-658C-579B-ED19FFD0EB5D}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_cryptography">
      <UniqueIdentifier>{21E7FA61-9E0A-4BA1-04B7-AF47AFA9CB8B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures\app_properties">
      <UniqueIdentifier>{632B4C79-AF7D-BFB5-D006-5AE67F607130}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures\undomanager">
      <UniqueIdentifier>{B10E20C2-4583-2B79-60B7-FE4D4B044313}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures\values">
      <UniqueIdentifier>{CFB54F15-8A8A-0505-9B7F-ECA41CEE38E8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_data_structures">
      <UniqueIdentifier>{911F0159-A7A8-4A43-3FD4-154F62F4A44B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\broadcasters">
      <UniqueIdentifier>{9D5816C2-E2B2-2E3F-B095-AC8BD1100D29}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\interprocess">
      <UniqueIdentifier>{3FDCD000-763F-8477-9AF8-70ABA2E91E5E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\messages">
      <UniqueIdentifier>{0947506F-66FA-EF8D-8A4E-4D48BCDBB226}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\native">
      <UniqueIdentifier>{E4B6AED3-F54C-3FF2-069F-640BACAE0E08}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events\timers">
      <UniqueIdentifier>{D5EADBCC-6A1C-C940-0206-26E49110AF08}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_events">
      <UniqueIdentifier>{D27DC92D-5BEB-9294-DCD1-81D54E245AD5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\colour">
      <UniqueIdentifier>{BCD73D20-42B1-6CDB-DE66-B06236A60F47}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\contexts">
      <UniqueIdentifier>{20DC13F6-2369-8841-9F0B-D13FA14EEE74}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\detail">
      <UniqueIdentifier>{0B30279D-5CEF-3E12-EA90-7D6CE4D52669}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\effects">
      <UniqueIdentifier>{A302A8DB-120F-9EBB-A3D5-2C29963AA56B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT">
      <UniqueIdentifier>{8C9420D9-C764-CBF5-0C95-45A68722E99A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR">
      <UniqueIdentifier>{3EC3A03F-5D43-25E3-C82E-688CDEB00C86}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL">
      <UniqueIdentifier>{462DE0A0-C61F-DAEC-457C-F2C331F6BA13}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix">
      <UniqueIdentifier>{DD7B3E15-B826-9CA6-34E6-AE5187A66799}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\svg">
      <UniqueIdentifier>{924CEEDF-081A-C4B8-B6CB-6579E15F7D71}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color">
      <UniqueIdentifier>{DAD6415C-4D8D-B181-3919-C63E674F1559}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf">
      <UniqueIdentifier>{A43A6434-A029-E35D-3205-06F6644E97E1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common">
      <UniqueIdentifier>{F3EA5987-9668-446D-7994-E5A2307673BC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF">
      <UniqueIdentifier>{F8F5D22C-0EF0-4673-4EA7-D67B35A3EFD3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS">
      <UniqueIdentifier>{6A1D4F6C-32A9-CCE3-AC75-2C4453748E1F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB">
      <UniqueIdentifier>{9F968C68-CB3F-7A0E-8D3F-620C10A2713F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout">
      <UniqueIdentifier>{D9EDBB3F-9EB4-BF65-7117-63C275BFFFE9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\name">
      <UniqueIdentifier>{1CD94C81-9754-F2A6-0E6F-53AD2CA6F254}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC">
      <UniqueIdentifier>{7B438AED-DDA8-FF31-67D3-4ABDF0BF11E4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var">
      <UniqueIdentifier>{C3F2648D-B6F9-6827-36EB-520E888C02D5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz\OT">
      <UniqueIdentifier>{E0C58D4E-D619-E6F8-E0E6-524676456033}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts\harfbuzz">
      <UniqueIdentifier>{37EDABAF-7289-F75A-B8D8-FF461ED0AC6B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\fonts">
      <UniqueIdentifier>{45489C2A-6E0E-CCDC-6638-0DACEEB63CCA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\geometry">
      <UniqueIdentifier>{F1B90726-DB55-0293-BFAF-C65C7DF5489C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\image_formats\jpglib">
      <UniqueIdentifier>{2C55FD42-0ACD-B0B8-7EAE-EB17F09BAEEC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\image_formats\pnglib">
      <UniqueIdentifier>{B68CD2B2-701F-9AB7-4638-2485D6E06BCF}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\image_formats">
      <UniqueIdentifier>{B0B7C78E-729E-0FFA-D611-82AE8BC7FE2C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\images">
      <UniqueIdentifier>{0A4F7E12-220C-14EF-0026-9C0629FA9C17}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\native">
      <UniqueIdentifier>{37F49E10-4E62-6D5C-FF70-722D0CA3D97E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\placement">
      <UniqueIdentifier>{160D9882-0F68-278D-C5F9-8960FD7421D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode\sheenbidi\Headers">
      <UniqueIdentifier>{0CB19E25-4E0F-3B63-F005-249DA1602485}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode\sheenbidi\Source">
      <UniqueIdentifier>{26969BB2-447E-3386-5885-61B4896FAA0E}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode\sheenbidi">
      <UniqueIdentifier>{5B44DF93-8EE9-BAF5-0B3B-30AE9E58A3F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics\unicode">
      <UniqueIdentifier>{A4846E15-C7B2-BB61-80BA-E284529F3AAA}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_graphics">
      <UniqueIdentifier>{4CED05DA-E0A2-E548-F753-1F2EF299A8E3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\accessibility\enums">
      <UniqueIdentifier>{46AE69B8-AD58-4381-6CDE-25C8D75B01D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\accessibility\interfaces">
      <UniqueIdentifier>{E56CB4FC-32E8-8740-A3BB-B323CD937A99}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\accessibility">
      <UniqueIdentifier>{4ECDCA0C-BB38-0729-A6B6-2FB0B4D0863B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\application">
      <UniqueIdentifier>{294E4CD5-B06F-97D1-04A3-51871CEA507C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\buttons">
      <UniqueIdentifier>{77228F15-BD91-06FF-2C7E-0377D25C2C94}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\commands">
      <UniqueIdentifier>{5CB531E6-BF9A-2C50-056C-EE5A525D28D3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\components">
      <UniqueIdentifier>{E4EA47E5-B41C-2A19-1783-7E9104096ECD}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\desktop">
      <UniqueIdentifier>{B331BC33-9770-3DB5-73F2-BC2469ECCF7F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\detail">
      <UniqueIdentifier>{3B09E947-B78C-1758-E072-7FD67F8DCB00}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\drawables">
      <UniqueIdentifier>{46A17AC9-0BFF-B5CE-26D6-B9D1992C88AC}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\filebrowser">
      <UniqueIdentifier>{D90A8DF7-FBAB-D363-13C0-6707BB22B72B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\keyboard">
      <UniqueIdentifier>{8AE77C40-6839-EC37-4515-BD3CC269BCE4}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\layout">
      <UniqueIdentifier>{0EAD99DB-011F-09E5-45A2-365F646EB004}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\lookandfeel">
      <UniqueIdentifier>{F57590C6-3B90-1BE1-1006-488BA33E8BD9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\menus">
      <UniqueIdentifier>{7C319D73-0D93-5842-0874-398D2D3038D5}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\misc">
      <UniqueIdentifier>{2CB4DB0C-DD3B-6195-D822-76EC7A5C88D2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\mouse">
      <UniqueIdentifier>{FE3CB19C-EF43-5CF5-DAF0-09D4E43D0AB9}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\native\accessibility">
      <UniqueIdentifier>{C0E5DD5D-F8F1-DD25-67D7-291946AB3828}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\native">
      <UniqueIdentifier>{895C2D33-E08D-B1BA-BB36-FC4CA65090C8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\positioning">
      <UniqueIdentifier>{D64A57DB-A956-5519-1929-1D929B56E1B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\properties">
      <UniqueIdentifier>{5A99CC24-AC45-7ED6-C11A-B8B86E76D884}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\widgets">
      <UniqueIdentifier>{7A131EEC-25A7-22F6-2839-A2194DDF3007}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics\windows">
      <UniqueIdentifier>{EA9DB76C-CEF7-6BFC-2070-28B7DF8E8063}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_basics">
      <UniqueIdentifier>{3C206A40-6F1B-E683-ACF1-DEC3703D0140}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\code_editor">
      <UniqueIdentifier>{DF95D4BF-E18C-125A-5EBB-8993A06E232C}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\detail">
      <UniqueIdentifier>{E0FCBD5F-0B11-D78C-F786-52AB7FEE2383}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\documents">
      <UniqueIdentifier>{118946F2-AC24-0F09-62D5-753DF87A60CD}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\embedding">
      <UniqueIdentifier>{07329F9B-7D3D-CEB3-C771-714842076140}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\misc">
      <UniqueIdentifier>{08BBBECB-B0D1-7611-37EC-F57E1D0CE2A2}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra\native">
      <UniqueIdentifier>{268E8F2A-980C-BF2F-B161-AACABC9D91F3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_gui_extra">
      <UniqueIdentifier>{A4D76113-9EDC-DA60-D89B-5BACF7F1C426}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_opengl\geometry">
      <UniqueIdentifier>{1A9221A3-E993-70B2-6EA2-8E1DB5FF646A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_opengl\native">
      <UniqueIdentifier>{CC2DAD7A-5B45-62AB-4C54-6FE6B1AE86C3}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_opengl\opengl">
      <UniqueIdentifier>{599138A9-EA63-53DD-941F-ABE3412D2949}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_opengl\utils">
      <UniqueIdentifier>{422A4014-8587-1AE6-584F-32A62613A37B}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_opengl">
      <UniqueIdentifier>{9FBFF5E5-56F1-34A1-2C85-F760DA2B1EB7}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_osc\osc">
      <UniqueIdentifier>{EEE9B92C-AD26-4BEA-4D95-3F859090EA9F}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules\juce_osc">
      <UniqueIdentifier>{B1DE8DB1-C00A-12C0-D690-8B3C9504A60A}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Modules">
      <UniqueIdentifier>{FE955B6B-68AC-AA07-70D8-2413F6DB65C8}</UniqueIdentifier>
    </Filter>
    <Filter Include="JUCE Library Code">
      <UniqueIdentifier>{7ED5A90E-41AF-A1EF-659B-37CEEAB9BA61}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\Source\Main.cpp">
      <Filter>NetworkGraphicsDemo\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\audio_play_head\juce_AudioPlayHead.cpp">
      <Filter>JUCE Modules\juce_audio_basics\audio_play_head</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioChannelSet.cpp">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioDataConverters.cpp">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioProcessLoadMeasurer.cpp">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_FloatVectorOperations.cpp">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMP_test.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPIterator.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPMidi1ToMidi2DefaultTranslator.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPSysEx7.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPUtils.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPView.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiBuffer.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiFile.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiKeyboardState.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiMessage.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiMessageSequence.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiRPN.cpp">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEInstrument.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEMessages.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPENote.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPESynthesiser.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPESynthesiserBase.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPESynthesiserVoice.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEUtils.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEValue.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEZoneLayout.cpp">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_BufferingAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ChannelRemappingAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_IIRFilterAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_MemoryAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_MixerAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_PositionableAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ResamplingAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ReverbAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ToneGeneratorAudioSource.cpp">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\synthesisers\juce_Synthesiser.cpp">
      <Filter>JUCE Modules\juce_audio_basics\synthesisers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_ADSR_test.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_AudioWorkgroup.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_IIRFilter.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_Interpolators.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_LagrangeInterpolator.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_SmoothedValue.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_WindowedSincInterpolator.cpp">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\juce_audio_basics.cpp">
      <Filter>JUCE Modules\juce_audio_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_basics\juce_audio_basics.mm">
      <Filter>JUCE Modules\juce_audio_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_AudioDeviceManager.cpp">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_AudioIODevice.cpp">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_AudioIODeviceType.cpp">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_SampleRateHelpers.cpp">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\midi_io\juce_MidiDeviceListConnectionBroadcaster.cpp">
      <Filter>JUCE Modules\juce_audio_devices\midi_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\midi_io\juce_MidiDevices.cpp">
      <Filter>JUCE Modules\juce_audio_devices\midi_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\midi_io\juce_MidiMessageCollector.cpp">
      <Filter>JUCE Modules\juce_audio_devices\midi_io</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\aaudio\AAudioLoader.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\aaudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\aaudio\AudioStreamAAudio.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\aaudio</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AdpfWrapper.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AudioSourceCaller.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AudioStream.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AudioStreamBuilder.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\DataConversionFlowGraph.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FilterAudioStream.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FixedBlockAdapter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FixedBlockReader.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FixedBlockWriter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\LatencyTuner.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\OboeExtensions.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\QuirksManager.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceFloatCaller.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceI16Caller.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceI24Caller.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceI32Caller.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\StabilizedCallback.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\Trace.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\Utilities.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\Version.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\fifo\FifoBuffer.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\fifo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\fifo\FifoController.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\fifo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\fifo\FifoControllerBase.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\fifo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\fifo\FifoControllerIndirect.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\fifo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\IntegerRatio.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\LinearResampler.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\MultiChannelResampler.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResampler.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerMono.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerStereo.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResampler.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResamplerStereo.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\ChannelCountConverter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\ClipToRange.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\FlowGraphNode.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\Limiter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\ManyToMultiConverter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoBlend.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoToMultiConverter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToManyConverter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToMonoConverter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\RampLinear.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SampleRateConverter.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkFloat.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI8_24.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI16.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI24.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI32.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceFloat.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI8_24.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI16.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI24.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI32.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioInputStreamOpenSLES.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioOutputStreamOpenSLES.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamBuffered.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamOpenSLES.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\EngineOpenSLES.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\OpenSLESUtilities.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\OutputMixerOpenSLES.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_ALSA_linux.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_ASIO_windows.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Audio_android.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Audio_ios.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Bela_linux.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_CoreAudio_mac.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_CoreMidi_mac.mm">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_DirectSound_windows.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_JackAudio.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Midi_android.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Midi_linux.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Midi_windows.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_Oboe_android.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_OpenSL_android.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\native\juce_WASAPI_windows.cpp">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\sources\juce_AudioSourcePlayer.cpp">
      <Filter>JUCE Modules\juce_audio_devices\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\sources\juce_AudioTransportSource.cpp">
      <Filter>JUCE Modules\juce_audio_devices\sources</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\juce_audio_devices.cpp">
      <Filter>JUCE Modules\juce_audio_devices</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_devices\juce_audio_devices.mm">
      <Filter>JUCE Modules\juce_audio_devices</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\deduplication\bitreader_read_rice_signed_block.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\deduplication</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\deduplication\lpc_compute_autocorrelation_intrin.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\deduplication</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\deduplication\lpc_compute_autocorrelation_intrin_neon.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\deduplication</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\bitmath.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\bitreader.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\bitwriter.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\cpu.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\crc.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\fixed.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\float.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\format.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\lpc_flac.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\lpc_intrin_neon.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\md5.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\memory.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\stream_decoder.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\stream_encoder.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\stream_encoder_framing.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\window_flac.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\analysis.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\bitrate.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\block.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\codebook.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\envelope.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\floor0.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\floor1.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\info.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lookup.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lpc.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lsp.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\mapping0.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\mdct.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\misc.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\psy.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\registry.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\res0.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\sharedbook.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\smallft.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\synthesis.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\vorbisenc.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\vorbisfile.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\window.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\bitwise.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\framing.c">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_AiffAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_CoreAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_FlacAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_LAMEEncoderAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_MP3AudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_OggVorbisAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_WavAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_WindowsMediaAudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_ARAAudioReaders.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormat.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatManager.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatReader.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatReaderSource.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatWriter.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioSubsectionReader.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\format\juce_BufferingAudioFormatReader.cpp">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\sampler\juce_Sampler.cpp">
      <Filter>JUCE Modules\juce_audio_formats\sampler</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\juce_audio_formats.cpp">
      <Filter>JUCE Modules\juce_audio_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_formats\juce_audio_formats.mm">
      <Filter>JUCE Modules\juce_audio_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format\juce_AudioPluginFormat.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format\juce_AudioPluginFormatManager.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix\tree.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\collections.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\filesystem.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\instance.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\lib.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\node.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\plugin.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\pluginclass.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\port.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\query.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\scalepoint.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\state.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\ui.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\util.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\world.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\atom-test-utils.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\atom-test.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\forge-overflow-test.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\base64.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\byte_source.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\env.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\n3.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\node.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\reader.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\serdi.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\string.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\system.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\uri.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\writer.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\btree.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\digest.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\hash.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_test.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_validate.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sordi.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sordmm_test.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\syntax.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sratom\src\sratom.c">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sratom\src</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\baseiids.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fbuffer.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fdebug.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fobject.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstreamer.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstring.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\updatehandler.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\thread\source\flock.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\thread\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\conststringtable.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\coreiids.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\funknown.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ustring.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples\vst-utilities\moduleinfotool\source\main.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\samples\vst-utilities\moduleinfotool\source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\commonstringconvert.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\memorystream.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\pluginview.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\readfile.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\hostclasses.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module_linux.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module_mac.mm">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module_win32.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\pluginterfacesupport.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfocreator.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfoparser.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\stringconvert.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\vst2persistence.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstbus.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponent.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponentbase.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vsteditcontroller.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstinitiids.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstparameters.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstpresetfile.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_ARACommon.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_ARAHosting.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_AudioUnitPluginFormat.mm">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LADSPAPluginFormat.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LegacyAudioParameter.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LV2PluginFormat.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LV2PluginFormat_test.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LV2SupportLibs.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VST3PluginFormat.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VST3PluginFormat_test.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VSTPluginFormat.cpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioPluginInstance.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessor.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorEditor.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorGraph.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorParameterGroup.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_GenericAudioProcessorEditor.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\processors\juce_PluginDescription.cpp">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\scanning\juce_KnownPluginList.cpp">
      <Filter>JUCE Modules\juce_audio_processors\scanning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\scanning\juce_PluginDirectoryScanner.cpp">
      <Filter>JUCE Modules\juce_audio_processors\scanning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\scanning\juce_PluginListComponent.cpp">
      <Filter>JUCE Modules\juce_audio_processors\scanning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARA_utils.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARADocumentController.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARADocumentControllerCommon.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARAModelObjects.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARAPlugInInstanceRoles.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_AudioProcessor_ARAExtensions.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AAXClientExtensions.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterBool.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterChoice.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterFloat.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterInt.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioProcessorParameterWithID.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioProcessorValueTreeState.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_ParameterAttachments.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_PluginHostType.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_RangedAudioParameter.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_VST2ClientExtensions.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_VST3ClientExtensions.cpp">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\juce_audio_processors.cpp">
      <Filter>JUCE Modules\juce_audio_processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\juce_audio_processors.mm">
      <Filter>JUCE Modules\juce_audio_processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\juce_audio_processors_ara.cpp">
      <Filter>JUCE Modules\juce_audio_processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_processors\juce_audio_processors_lv2_libs.cpp">
      <Filter>JUCE Modules\juce_audio_processors</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\audio_cd\juce_AudioCDReader.cpp">
      <Filter>JUCE Modules\juce_audio_utils\audio_cd</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioAppComponent.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioDeviceSelectorComponent.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioThumbnail.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioThumbnailCache.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioVisualiserComponent.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_KeyboardComponentBase.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_MidiKeyboardComponent.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\gui\juce_MPEKeyboardComponent.cpp">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_AudioCDBurner_mac.mm">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_AudioCDBurner_windows.cpp">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_AudioCDReader_linux.cpp">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_AudioCDReader_mac.mm">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_AudioCDReader_windows.cpp">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_android.cpp">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_ios.mm">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_linux.cpp">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_mac.mm">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\native\juce_BluetoothMidiDevicePairingDialogue_windows.cpp">
      <Filter>JUCE Modules\juce_audio_utils\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\players\juce_AudioProcessorPlayer.cpp">
      <Filter>JUCE Modules\juce_audio_utils\players</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\players\juce_SoundPlayer.cpp">
      <Filter>JUCE Modules\juce_audio_utils\players</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\juce_audio_utils.cpp">
      <Filter>JUCE Modules\juce_audio_utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_audio_utils\juce_audio_utils.mm">
      <Filter>JUCE Modules\juce_audio_utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_AbstractFifo.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_ArrayBase.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_DynamicObject.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_Enumerate_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_FixedSizeFunction_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_HashMap_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_ListenerList_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_NamedValueSet.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_Optional_test.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_OwnedArray.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_PropertySet.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_ReferenceCountedArray.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_SparseSet.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\containers\juce_Variant.cpp">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_common_MimeTypes.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_DirectoryIterator.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_File.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileFilter.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileInputStream.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileOutputStream.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_FileSearchPath.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_RangedDirectoryIterator.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_TemporaryFile.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\files\juce_WildcardFileFilter.cpp">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\json\juce_JSON.cpp">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\json\juce_JSONSerialisation_test.cpp">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\json\juce_JSONUtils.cpp">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\logging\juce_FileLogger.cpp">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\logging\juce_Logger.cpp">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_BigInteger.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_Expression.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_MathsFunctions_test.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\maths\juce_Random.cpp">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\memory\juce_AllocationHooks.cpp">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\memory\juce_MemoryBlock.cpp">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\memory\juce_SharedResourcePointer_test.cpp">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_ConsoleApplication.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_EnumHelpers_test.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_Result.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_RuntimePermissions.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_ScopeGuard.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\misc\juce_Uuid.cpp">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_AndroidDocument_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_CommonFile_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Files_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_JNIHelpers_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Misc_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_NamedPipe_posix.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_curl.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Network_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_ObjCHelpers_mac_test.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_PlatformTimer_generic.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_PlatformTimer_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Process_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Registry_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_RuntimePermissions_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Strings_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_wasm.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_SystemStats_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_android.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_linux.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_mac.mm">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\native\juce_Threads_windows.cpp">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_IPAddress.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_MACAddress.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_NamedPipe.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_Socket.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_URL.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\network\juce_WebInputStream.cpp">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_BufferedInputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_FileInputSource.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_InputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_MemoryInputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_MemoryOutputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_OutputStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_SubregionStream.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\streams\juce_URLInputSource.cpp">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\system\juce_SystemStats.cpp">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_Base64.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharacterFunctions.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF8_test.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF16_test.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF32_test.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_Identifier.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_LocalisedStrings.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_String.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_StringArray.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_StringPairArray.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_StringPool.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\text\juce_TextDiff.cpp">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_ChildProcess.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_HighResolutionTimer.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_ReadWriteLock.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_Thread.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_ThreadPool.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_TimeSliceThread.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\threads\juce_WaitableEvent.cpp">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\time\juce_PerformanceCounter.cpp">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\time\juce_RelativeTime.cpp">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\time\juce_Time.cpp">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\unit_tests\juce_UnitTest.cpp">
      <Filter>JUCE Modules\juce_core\unit_tests</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\xml\juce_XmlDocument.cpp">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\xml\juce_XmlElement.cpp">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\adler32.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\compress.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\crc32.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\deflate.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\infback.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\inffast.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\inflate.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\inftrees.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\trees.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\uncompr.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\zlib\zutil.c">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\juce_GZIPCompressorOutputStream.cpp">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\juce_GZIPDecompressorInputStream.cpp">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\zip\juce_ZipFile.cpp">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\juce_core.cpp">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\juce_core.mm">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_core\juce_core_CompilationTime.cpp">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\encryption\juce_BlowFish.cpp">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\encryption\juce_Primes.cpp">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\encryption\juce_RSAKey.cpp">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\hashing\juce_MD5.cpp">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\hashing\juce_SHA256.cpp">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\hashing\juce_Whirlpool.cpp">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\juce_cryptography.cpp">
      <Filter>JUCE Modules\juce_cryptography</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_cryptography\juce_cryptography.mm">
      <Filter>JUCE Modules\juce_cryptography</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_ApplicationProperties.cpp">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_PropertiesFile.cpp">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoableAction.cpp">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoManager.cpp">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_CachedValue.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_Value.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTree.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreePropertyWithDefault_test.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.cpp">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\juce_data_structures.cpp">
      <Filter>JUCE Modules\juce_data_structures</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_data_structures\juce_data_structures.mm">
      <Filter>JUCE Modules\juce_data_structures</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_ActionBroadcaster.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_AsyncUpdater.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_ChangeBroadcaster.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\broadcasters\juce_LockingAsyncUpdater.cpp">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_ChildProcessManager.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_ConnectedChildProcess.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnection.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnectionServer.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\interprocess\juce_NetworkServiceDiscovery.cpp">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_ApplicationBase.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_DeletedAtShutdown.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_MessageListener.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\messages\juce_MessageManager.cpp">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_MessageManager_ios.mm">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_MessageManager_mac.mm">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_Messaging_android.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_Messaging_linux.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_Messaging_windows.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_ScopedLowPowerModeDisabler.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\native\juce_WinRTWrapper_windows.cpp">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\timers\juce_MultiTimer.cpp">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\timers\juce_Timer.cpp">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\juce_events.cpp">
      <Filter>JUCE Modules\juce_events</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_events\juce_events.mm">
      <Filter>JUCE Modules\juce_events</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_Colour.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_ColourGradient.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_Colours.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\colour\juce_FillType.cpp">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\contexts\juce_GraphicsContext.cpp">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.cpp">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_JustifiedText.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_Ranges.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_ShapedText.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\detail\juce_SimpleShapedText.cpp">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\effects\juce_DropShadowEffect.cpp">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\effects\juce_GlowEffect.cpp">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\VARC.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\failing-alloc.c">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\harfbuzz-subset.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\harfbuzz.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-map.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-blob.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-serialize.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-verify.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-common.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-coretext-font.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-coretext-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-directwrite.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-draw.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face-builder.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-fallback-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-font.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ft.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gdi.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-glib.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gobject-structs.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-graphite2.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-icu.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-map.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-number.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-table.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff2-table.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-color.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-face.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-font.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-map.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-fallback.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-normalize.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-default.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-hangul.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-hebrew.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic-table.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-khmer.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-myanmar.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-syllabic.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-thai.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-vowel-constraints.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-outline.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint-extents.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-static.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-style.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff-common.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff1.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff2.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-input.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-iup.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-solver.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-repacker.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ucd.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-uniscribe.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-shape.cc">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_AttributedString.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_Font.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_FontOptions.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_GlyphArrangement.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_TextLayout.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_Typeface.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\fonts\juce_TypefaceTestData.cpp">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_AffineTransform.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_EdgeTable.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_Parallelogram_test.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_Path.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathIterator.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathStrokeType.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\geometry\juce_Rectangle_test.cpp">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcapimin.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcapistd.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jccoefct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jccolor.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcdctmgr.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jchuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcinit.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcmainct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcmarker.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcmaster.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcomapi.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcparam.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcphuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcprepct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jcsample.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jctrans.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdapimin.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdapistd.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdatasrc.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdcoefct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdcolor.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jddctmgr.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdhuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdinput.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmainct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmarker.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmaster.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdmerge.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdphuff.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdpostct.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdsample.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdtrans.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jerror.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jfdctflt.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jfdctfst.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jfdctint.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctflt.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctfst.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctint.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jidctred.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmemmgr.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmemnobs.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jquant1.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jquant2.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jutils.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\transupp.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\png.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngerror.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngget.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngmem.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngpread.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngread.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngrio.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngrtran.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngrutil.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngset.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngtrans.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwio.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwrite.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwtran.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngwutil.c">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\juce_GIFLoader.cpp">
      <Filter>JUCE Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\juce_JPEGLoader.cpp">
      <Filter>JUCE Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\image_formats\juce_PNGLoader.cpp">
      <Filter>JUCE Modules\juce_graphics\image_formats</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_Image.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_ImageCache.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_ImageConvolutionKernel.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\images\juce_ImageFileFormat.cpp">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_CoreGraphicsContext_mac.mm">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DGraphicsContext_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DHelpers_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DHwndContext_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImage_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImageContext_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DMetrics_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DResources_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_DirectWriteTypeface_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_android.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_freetype.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_linux.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_Fonts_mac.mm">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_GraphicsContext_android.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_android.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_linux.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_mac.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\native\juce_IconHelpers_windows.cpp">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\placement\juce_RectanglePlacement.cpp">
      <Filter>JUCE Modules\juce_graphics\placement</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiChain.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiTypeLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BracketQueue.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\GeneralCategoryLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\IsolatingRun.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\LevelRun.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\PairingLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunQueue.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBAlgorithm.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBBase.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBCodepointSequence.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLine.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLog.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBMirrorLocator.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBParagraph.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBScriptLocator.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptLookup.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptStack.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SheenBidi.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\StatusStack.c">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_Unicode.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeBidi.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeGenerated.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeLine.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeScript.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\unicode\juce_UnicodeUtils.cpp">
      <Filter>JUCE Modules\juce_graphics\unicode</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics.cpp">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics.mm">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics_Harfbuzz.cpp">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_graphics\juce_graphics_Sheenbidi.c">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\accessibility\juce_AccessibilityHandler.cpp">
      <Filter>JUCE Modules\juce_gui_basics\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\application\juce_Application.cpp">
      <Filter>JUCE Modules\juce_gui_basics\application</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ArrowButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_Button.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_DrawableButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_HyperlinkButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ImageButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ShapeButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_TextButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToggleButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToolbarButton.cpp">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.cpp">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_Component.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_ComponentListener.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_FocusTraverser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\components\juce_ModalComponentManager.cpp">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Desktop.cpp">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Displays.cpp">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\detail\juce_AccessibilityHelpers.cpp">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ComponentPeerHelpers.cpp">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_Drawable.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableComposite.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableImage.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawablePath.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableRectangle.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableShape.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableText.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_SVGParser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ContentSharer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileListComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_CaretComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyListener.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyPress.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_ModifierKeys.cpp">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_BorderedComponentBoundsConstrainer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentAnimator.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBuilder.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ConcertinaPanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_FlexBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Grid.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GridItem.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GroupComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ScrollBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_SidePanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedButtonBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Viewport.cpp">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V4.cpp">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_BurgerMenuComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarModel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\menus\juce_PopupMenu.cpp">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\misc\juce_BubbleComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\misc\juce_DropShadower.cpp">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\misc\juce_FocusOutline.cpp">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_ComponentDragger.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseCursor.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseEvent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInputSource.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseListener.cpp">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_Accessibility_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityElement_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilitySharedCode_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityTextHelpers_test.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_ContentSharer_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_ContentSharer_ios.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_DragAndDrop_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_DragAndDrop_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_FileChooser_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_MainMenu_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_MouseCursor_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeMessageBox_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_NSViewComponentPeer_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedDPIAwarenessDisabler.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_UIViewComponentPeer_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_VBlank_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_Windowing_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowsHooks_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_android.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_ios.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_mac.mm">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowUtils_windows.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_XSymbols_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\native\juce_XWindowSystem_linux.cpp">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_MarkerList.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePoint.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePointPath.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeRectangle.cpp">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_MultiChoicePropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyPanel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\properties\juce_TextPropertyComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ComboBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ImageComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Label.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ListBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ProgressBar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Slider.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableListBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TextEditor.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TextEditorModel.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Toolbar.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TreeView.cpp">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_AlertWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_CallOutBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ComponentPeer.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DialogWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DocumentWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_MessageBoxOptions.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeMessageBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeScaleFactorNotifier.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ResizableWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ScopedMessageBox.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TooltipWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TopLevelWindow.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\windows\juce_VBlankAttachment.cpp">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\juce_gui_basics.cpp">
      <Filter>JUCE Modules\juce_gui_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_basics\juce_gui_basics.mm">
      <Filter>JUCE Modules\juce_gui_basics</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeDocument.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.cpp">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\documents\juce_FileBasedDocument.cpp">
      <Filter>JUCE Modules\juce_gui_extra\documents</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_ColourSelector.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_LiveConstantEditor.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PreferencesPanel.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PushNotifications.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SplashScreen.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebBrowserComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebControlRelays.cpp">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_ActiveXComponent_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_AndroidViewComponent.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_AppleRemote_mac.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_HWNDComponent_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_NSViewComponent_mac.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_PushNotifications_android.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_PushNotifications_ios.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_PushNotifications_mac.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_SystemTrayIcon_linux.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_SystemTrayIcon_mac.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_SystemTrayIcon_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_UIViewComponent_ios.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_android.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_linux.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_mac.mm">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_WebBrowserComponent_windows.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\native\juce_XEmbedComponent_linux.cpp">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\juce_gui_extra.cpp">
      <Filter>JUCE Modules\juce_gui_extra</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_gui_extra\juce_gui_extra.mm">
      <Filter>JUCE Modules\juce_gui_extra</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_gl.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_gles2.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLContext.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLFrameBuffer.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLGraphicsContext.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLHelpers.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLImage.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLPixelFormat.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLShaderProgram.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLTexture.cpp">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\utils\juce_OpenGLAppComponent.cpp">
      <Filter>JUCE Modules\juce_opengl\utils</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\juce_opengl.cpp">
      <Filter>JUCE Modules\juce_opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_opengl\juce_opengl.mm">
      <Filter>JUCE Modules\juce_opengl</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCAddress.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCArgument.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCBundle.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCMessage.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCReceiver.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCSender.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCTimeTag.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\osc\juce_OSCTypes.cpp">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\..\modules\juce_osc\juce_osc.cpp">
      <Filter>JUCE Modules\juce_osc</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\BinaryData.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_basics.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_devices.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_formats.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors_ara.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_processors_lv2_libs.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_audio_utils.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_core_CompilationTime.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_cryptography.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_data_structures.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_events.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Harfbuzz.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_graphics_Sheenbidi.c">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_basics.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_gui_extra.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_opengl.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
    <ClCompile Include="..\..\JuceLibraryCode\include_juce_osc.cpp">
      <Filter>JUCE Library Code</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Source\Demos.h">
      <Filter>NetworkGraphicsDemo\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\MasterComponent.h">
      <Filter>NetworkGraphicsDemo\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\ClientComponent.h">
      <Filter>NetworkGraphicsDemo\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Source\SharedCanvas.h">
      <Filter>NetworkGraphicsDemo\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\audio_play_head\juce_AudioPlayHead.h">
      <Filter>JUCE Modules\juce_audio_basics\audio_play_head</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioChannelSet.h">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioDataConverters.h">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioProcessLoadMeasurer.h">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_AudioSampleBuffer.h">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\buffers\juce_FloatVectorOperations.h">
      <Filter>JUCE Modules\juce_audio_basics\buffers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMP.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPacket.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPackets.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPBytesOnGroup.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPConversion.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPConverters.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPDeviceInfo.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPDispatcher.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPFactory.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPIterator.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPMidi1ToBytestreamTranslator.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPMidi1ToMidi2DefaultTranslator.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPProtocols.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPReceiver.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPSysEx7.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPUtils.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\ump\juce_UMPView.h">
      <Filter>JUCE Modules\juce_audio_basics\midi\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiBuffer.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiDataConcatenator.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiFile.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiKeyboardState.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiMessage.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiMessageSequence.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\midi\juce_MidiRPN.h">
      <Filter>JUCE Modules\juce_audio_basics\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEInstrument.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEMessages.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPENote.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPESynthesiser.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPESynthesiserBase.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPESynthesiserVoice.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEUtils.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEValue.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\mpe\juce_MPEZoneLayout.h">
      <Filter>JUCE Modules\juce_audio_basics\mpe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\native\juce_AudioWorkgroup_mac.h">
      <Filter>JUCE Modules\juce_audio_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\native\juce_CoreAudioLayouts_mac.h">
      <Filter>JUCE Modules\juce_audio_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\native\juce_CoreAudioTimeConversions_mac.h">
      <Filter>JUCE Modules\juce_audio_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_AudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_BufferingAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ChannelRemappingAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_IIRFilterAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_MemoryAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_MixerAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_PositionableAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ResamplingAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ReverbAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\sources\juce_ToneGeneratorAudioSource.h">
      <Filter>JUCE Modules\juce_audio_basics\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\synthesisers\juce_Synthesiser.h">
      <Filter>JUCE Modules\juce_audio_basics\synthesisers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_ADSR.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_AudioWorkgroup.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_Decibels.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_GenericInterpolator.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_IIRFilter.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_Interpolators.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_Reverb.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\utilities\juce_SmoothedValue.h">
      <Filter>JUCE Modules\juce_audio_basics\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_basics\juce_audio_basics.h">
      <Filter>JUCE Modules\juce_audio_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_AudioDeviceManager.h">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_AudioIODevice.h">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_AudioIODeviceType.h">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\audio_io\juce_SystemAudioVolume.h">
      <Filter>JUCE Modules\juce_audio_devices\audio_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\midi_io\ump\juce_UMPBytestreamInputHandler.h">
      <Filter>JUCE Modules\juce_audio_devices\midi_io\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\midi_io\ump\juce_UMPU32InputHandler.h">
      <Filter>JUCE Modules\juce_audio_devices\midi_io\ump</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\midi_io\juce_MidiDevices.h">
      <Filter>JUCE Modules\juce_audio_devices\midi_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\midi_io\juce_MidiMessageCollector.h">
      <Filter>JUCE Modules\juce_audio_devices\midi_io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\AudioStream.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\AudioStreamBase.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\AudioStreamBuilder.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\AudioStreamCallback.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\Definitions.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\FifoBuffer.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\FifoControllerBase.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\FullDuplexStream.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\LatencyTuner.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\Oboe.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\OboeExtensions.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\ResultWithValue.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\StabilizedCallback.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\Utilities.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\include\oboe\Version.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\include\oboe</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\aaudio\AAudioExtensions.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\aaudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\aaudio\AAudioLoader.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\aaudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\aaudio\AudioStreamAAudio.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\aaudio</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AdpfWrapper.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AudioClock.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\AudioSourceCaller.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\DataConversionFlowGraph.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FilterAudioStream.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FixedBlockAdapter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FixedBlockReader.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\FixedBlockWriter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\MonotonicCounter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\OboeDebug.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\QuirksManager.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceFloatCaller.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceI16Caller.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceI24Caller.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\SourceI32Caller.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\Trace.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\fifo\FifoController.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\fifo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\fifo\FifoControllerIndirect.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\fifo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\HyperbolicCosineWindow.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\IntegerRatio.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\KaiserWindow.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\LinearResampler.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\MultiChannelResampler.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResampler.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerMono.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\PolyphaseResamplerStereo.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\ResamplerDefinitions.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResampler.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\SincResamplerStereo.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\ChannelCountConverter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\ClipToRange.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\FlowGraphNode.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\FlowgraphUtilities.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\Limiter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\ManyToMultiConverter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoBlend.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MonoToMultiConverter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToManyConverter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\MultiToMonoConverter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\RampLinear.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SampleRateConverter.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkFloat.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI8_24.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI16.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI24.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SinkI32.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceFloat.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI8_24.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI16.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI24.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\SourceI32.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioInputStreamOpenSLES.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioOutputStreamOpenSLES.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamBuffered.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\AudioStreamOpenSLES.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\EngineOpenSLES.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\OpenSLESUtilities.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\opensles\OutputMixerOpenSLES.h">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\opensles</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\juce_Audio_ios.h">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\native\juce_HighPerformanceAudioHelpers_android.h">
      <Filter>JUCE Modules\juce_audio_devices\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\sources\juce_AudioSourcePlayer.h">
      <Filter>JUCE Modules\juce_audio_devices\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\sources\juce_AudioTransportSource.h">
      <Filter>JUCE Modules\juce_audio_devices\sources</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_devices\juce_audio_devices.h">
      <Filter>JUCE Modules\juce_audio_devices</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitmath.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitreader.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\bitwriter.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\cpu.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\crc.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\fixed.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\float.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\format.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\lpc.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\md5.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\memory.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\stream_encoder.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\stream_encoder_framing.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\private\window.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\private</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\stream_decoder.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\libFLAC\include\protected\stream_encoder.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac\libFLAC\include\protected</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\all.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\alloc.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\assert.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\callback.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\compat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\endswap.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\export.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\format.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\metadata.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\ordinals.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\private.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\stream_decoder.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\stream_encoder.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled\res_books_51.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled\res_books_stereo.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\coupled</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\floor\floor_books.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\floor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\uncoupled\res_books_uncoupled.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\books\uncoupled</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\floor_all.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_8.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_11.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_16.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\psych_44.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_8.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_16.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_44.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_44p51.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\residue_44u.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_8.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_11.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_16.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_22.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_32.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_44.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_44p51.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_44u.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes\setup_X.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\modes</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\backends.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\bitrate.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\codebook.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\codec_internal.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\envelope.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\highlevel.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lookup.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lookup_data.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lpc.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\lsp.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\masking.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\mdct.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\misc.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\os.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\psy.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\registry.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\scales.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\smallft.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib\window.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\lib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\codec.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\config_types.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\crctable.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\ogg.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\os_types.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\vorbisenc.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\vorbisfile.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_AiffAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_CoreAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_FlacAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_LAMEEncoderAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_MP3AudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_OggVorbisAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_WavAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\codecs\juce_WindowsMediaAudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\codecs</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_ARAAudioReaders.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormat.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatManager.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatReader.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatReaderSource.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioFormatWriter.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_AudioSubsectionReader.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_BufferingAudioFormatReader.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\format\juce_MemoryMappedAudioFormatReader.h">
      <Filter>JUCE Modules\juce_audio_formats\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\sampler\juce_Sampler.h">
      <Filter>JUCE Modules\juce_audio_formats\sampler</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_formats\juce_audio_formats.h">
      <Filter>JUCE Modules\juce_audio_formats</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format\juce_AudioPluginFormat.h">
      <Filter>JUCE Modules\juce_audio_processors\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format\juce_AudioPluginFormatManager.h">
      <Filter>JUCE Modules\juce_audio_processors\format</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv\lilv.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv\lilvmm.hpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\lilv</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix\common.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix\tree.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\zix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\filesystem.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv\src\lilv_internal.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lilv\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\atom.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\forge.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom\util.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\atom</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\buf-size\buf-size.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\buf-size</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core\attributes.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core\lv2.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core\lv2_util.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\data-access\data-access.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\data-access</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\dynmanifest\dynmanifest.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\dynmanifest</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event\event-helpers.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event\event.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\event</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\instance-access\instance-access.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\instance-access</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log\log.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log\logger.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\log</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\midi\midi.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\midi</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\morph\morph.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\morph</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\options\options.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\options</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\parameters\parameters.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\parameters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\patch\patch.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\patch</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-groups\port-groups.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-groups</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-props\port-props.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\port-props</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\presets\presets.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\presets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\resize-port\resize-port.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\resize-port</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\state\state.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\state</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\time\time.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\ui\ui.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\ui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\units\units.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\units</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\uri-map\uri-map.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\uri-map</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\urid\urid.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\urid</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\worker\worker.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\lv2\lv2\worker</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\serd\serd.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\serd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\attributes.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\base64.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\byte_sink.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\byte_source.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\node.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\reader.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\serd_config.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\serd_internal.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\stack.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\string_utils.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\system.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd\src\uri_utils.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\serd\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\sord\sord.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\sord</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\sord\sordmm.hpp">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\sord</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\btree.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\common.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\digest.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix\hash.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src\zix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_config.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord\src\sord_internal.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sord\src</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sratom\sratom\sratom.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK\sratom\sratom</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\juce_lv2_config.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\lilv_config.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\serd_config.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\sord_config.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\ipslcontextinfo.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\ipsleditcontroller.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\ipslgainreduction.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\ipslhostcommands.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\ipslviewembedding.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\ipslviewscaling.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\pslauextensions.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\pslextensions\pslvst2extensions.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\pslextensions</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\classfactoryhelpers.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fbuffer.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fcommandline.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fdebug.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fobject.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstreamer.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\fstring.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\source\updatehandler.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\thread\include\flock.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base\thread\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\conststringtable.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\falignpop.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\falignpush.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\fplatform.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\fstrdefs.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ftypes.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\funknown.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\funknownimpl.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\futils.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\fvariant.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ibstream.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\icloneable.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ipersistent.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ipluginbase.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\iplugincompatibility.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\istringresult.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\iupdatehandler.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\smartpointer.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\typesizecheck.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base\ustring.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui\iplugview.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui\iplugviewcontentscalesupport.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstattributes.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstaudioprocessor.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstautomationstate.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstchannelcontextinfo.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstcomponent.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstcontextmenu.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstdataexchange.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivsteditcontroller.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstevents.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivsthostapplication.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstinterappaudio.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstmessage.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstmidicontrollers.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstmidilearn.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstnoteexpression.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstparameterchanges.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstparameterfunctionname.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstphysicalui.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstpluginterfacesupport.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstplugview.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstprefetchablesupport.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstprocesscontext.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstremapparamid.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstrepresentation.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivsttestplugprovider.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\ivstunits.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\vstpshpack4.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\vstspeaker.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst\vsttypes.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\commonstringconvert.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\memorystream.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\pluginview.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common\readfile.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\hostclasses.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\module.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting\pluginterfacesupport.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\hosting</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\json.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\jsoncxx.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfo.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfocreator.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\moduleinfoparser.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\optional.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\stringconvert.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\uid.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility\vst2persistence.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\utility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstbus.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponent.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstcomponentbase.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vsteditcontroller.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstparameters.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\vstpresetfile.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_ARACommon.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_ARAHosting.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_AU_Shared.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_AudioUnitPluginFormat.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LADSPAPluginFormat.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LV2Common.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LV2PluginFormat.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_LV2Resources.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VST3Common.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VST3Headers.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VST3PluginFormat.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VSTCommon.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VSTMidiEventList.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\format_types\juce_VSTPluginFormat.h">
      <Filter>JUCE Modules\juce_audio_processors\format_types</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioPluginInstance.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessor.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorEditor.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorEditorHostContext.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorGraph.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorListener.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorParameter.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_AudioProcessorParameterGroup.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_GenericAudioProcessorEditor.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_HostedAudioProcessorParameter.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\processors\juce_PluginDescription.h">
      <Filter>JUCE Modules\juce_audio_processors\processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\scanning\juce_KnownPluginList.h">
      <Filter>JUCE Modules\juce_audio_processors\scanning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\scanning\juce_PluginDirectoryScanner.h">
      <Filter>JUCE Modules\juce_audio_processors\scanning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\scanning\juce_PluginListComponent.h">
      <Filter>JUCE Modules\juce_audio_processors\scanning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARA_utils.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARADebug.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARADocumentController.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARAModelObjects.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_ARAPlugInInstanceRoles.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\ARA\juce_AudioProcessor_ARAExtensions.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities\ARA</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AAXClientExtensions.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterBool.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterChoice.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterFloat.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioParameterInt.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioProcessorParameterWithID.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_AudioProcessorValueTreeState.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_ExtensionsVisitor.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_FlagCache.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_ParameterAttachments.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_PluginHostType.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_RangedAudioParameter.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_VST2ClientExtensions.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\utilities\juce_VST3ClientExtensions.h">
      <Filter>JUCE Modules\juce_audio_processors\utilities</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_processors\juce_audio_processors.h">
      <Filter>JUCE Modules\juce_audio_processors</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\audio_cd\juce_AudioCDBurner.h">
      <Filter>JUCE Modules\juce_audio_utils\audio_cd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\audio_cd\juce_AudioCDReader.h">
      <Filter>JUCE Modules\juce_audio_utils\audio_cd</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioAppComponent.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioDeviceSelectorComponent.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioThumbnail.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioThumbnailBase.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioThumbnailCache.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_AudioVisualiserComponent.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_BluetoothMidiDevicePairingDialogue.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_KeyboardComponentBase.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_MidiKeyboardComponent.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\gui\juce_MPEKeyboardComponent.h">
      <Filter>JUCE Modules\juce_audio_utils\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\players\juce_AudioProcessorPlayer.h">
      <Filter>JUCE Modules\juce_audio_utils\players</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\players\juce_SoundPlayer.h">
      <Filter>JUCE Modules\juce_audio_utils\players</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_audio_utils\juce_audio_utils.h">
      <Filter>JUCE Modules\juce_audio_utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_AbstractFifo.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Array.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ArrayAllocationBase.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ArrayBase.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_DynamicObject.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ElementComparator.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Enumerate.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_FixedSizeFunction.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_HashMap.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_LinkedListPointer.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ListenerList.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_NamedValueSet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Optional.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_OwnedArray.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_PropertySet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ReferenceCountedArray.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_ScopedValueSetter.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_SingleThreadedAbstractFifo.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_SortedSet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Span.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_SparseSet.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\containers\juce_Variant.h">
      <Filter>JUCE Modules\juce_core\containers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\detail\juce_CallbackListenerList.h">
      <Filter>JUCE Modules\juce_core\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\detail\juce_NativeFileHandle.h">
      <Filter>JUCE Modules\juce_core\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_AndroidDocument.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_common_MimeTypes.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_DirectoryIterator.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_File.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileFilter.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileInputStream.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileOutputStream.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_FileSearchPath.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_MemoryMappedFile.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_RangedDirectoryIterator.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_TemporaryFile.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\files\juce_WildcardFileFilter.h">
      <Filter>JUCE Modules\juce_core\files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\json\juce_JSON.h">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\json\juce_JSONSerialisation.h">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\json\juce_JSONUtils.h">
      <Filter>JUCE Modules\juce_core\json</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\logging\juce_FileLogger.h">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\logging\juce_Logger.h">
      <Filter>JUCE Modules\juce_core\logging</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_BigInteger.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_Expression.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_MathsFunctions.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_NormalisableRange.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_Random.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_Range.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\maths\juce_StatisticsAccumulator.h">
      <Filter>JUCE Modules\juce_core\maths</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_AllocationHooks.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Atomic.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ByteOrder.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ContainerDeletePolicy.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_CopyableHeapBlock.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_HeapBlock.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_HeavyweightLeakedObjectDetector.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_LeakedObjectDetector.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Memory.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_MemoryBlock.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_OptionalScopedPointer.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ReferenceCountedObject.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Reservoir.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_ScopedPointer.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_SharedResourcePointer.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_Singleton.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\memory\juce_WeakReference.h">
      <Filter>JUCE Modules\juce_core\memory</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_ConsoleApplication.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_EnumHelpers.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_Functional.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_OptionsHelpers.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_Result.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_RuntimePermissions.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_ScopeGuard.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_Uuid.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\misc\juce_WindowsRegistry.h">
      <Filter>JUCE Modules\juce_core\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_BasicNativeHeaders.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_CFHelpers_mac.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_ComSmartPtr_windows.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_IPAddress_posix.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_JNIHelpers_android.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_ObjCHelpers_mac.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_PlatformTimerListener.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_SharedCode_intel.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_SharedCode_posix.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\native\juce_ThreadPriorities_native.h">
      <Filter>JUCE Modules\juce_core\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_IPAddress.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_MACAddress.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_NamedPipe.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_Socket.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_URL.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\network\juce_WebInputStream.h">
      <Filter>JUCE Modules\juce_core\network</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\serialisation\juce_Serialisation.h">
      <Filter>JUCE Modules\juce_core\serialisation</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_AndroidDocumentInputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_BufferedInputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_FileInputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_InputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_InputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_MemoryInputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_MemoryOutputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_OutputStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_SubregionStream.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\streams\juce_URLInputSource.h">
      <Filter>JUCE Modules\juce_core\streams</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_CompilerSupport.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_CompilerWarnings.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_PlatformDefs.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_StandardHeader.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_SystemStats.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\system\juce_TargetPlatform.h">
      <Filter>JUCE Modules\juce_core\system</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_Base64.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharacterFunctions.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_ASCII.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF8.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF16.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_CharPointer_UTF32.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_Identifier.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_LocalisedStrings.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_NewLine.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_String.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringArray.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringPairArray.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringPool.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_StringRef.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\text\juce_TextDiff.h">
      <Filter>JUCE Modules\juce_core\text</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ChildProcess.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_CriticalSection.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_DynamicLibrary.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_HighResolutionTimer.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_InterProcessLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_Process.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ReadWriteLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ScopedLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ScopedReadLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ScopedWriteLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_SpinLock.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_Thread.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ThreadLocalValue.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_ThreadPool.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_TimeSliceThread.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\threads\juce_WaitableEvent.h">
      <Filter>JUCE Modules\juce_core\threads</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\time\juce_PerformanceCounter.h">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\time\juce_RelativeTime.h">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\time\juce_Time.h">
      <Filter>JUCE Modules\juce_core\time</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\unit_tests\juce_UnitTest.h">
      <Filter>JUCE Modules\juce_core\unit_tests</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\unit_tests\juce_UnitTestCategories.h">
      <Filter>JUCE Modules\juce_core\unit_tests</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\xml\juce_XmlDocument.h">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\xml\juce_XmlElement.h">
      <Filter>JUCE Modules\juce_core\xml</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\crc32.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\deflate.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\gzguts.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inffast.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inffixed.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inflate.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\inftrees.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\trees.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\zconf.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\zlib.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\zlib\zutil.h">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_GZIPCompressorOutputStream.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_GZIPDecompressorInputStream.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_ZipFile.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\zip\juce_zlib.h">
      <Filter>JUCE Modules\juce_core\zip</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_core\juce_core.h">
      <Filter>JUCE Modules\juce_core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\encryption\juce_BlowFish.h">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\encryption\juce_Primes.h">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\encryption\juce_RSAKey.h">
      <Filter>JUCE Modules\juce_cryptography\encryption</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\hashing\juce_MD5.h">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\hashing\juce_SHA256.h">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\hashing\juce_Whirlpool.h">
      <Filter>JUCE Modules\juce_cryptography\hashing</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_cryptography\juce_cryptography.h">
      <Filter>JUCE Modules\juce_cryptography</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_ApplicationProperties.h">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\app_properties\juce_PropertiesFile.h">
      <Filter>JUCE Modules\juce_data_structures\app_properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoableAction.h">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\undomanager\juce_UndoManager.h">
      <Filter>JUCE Modules\juce_data_structures\undomanager</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_CachedValue.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_Value.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTree.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreePropertyWithDefault.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\values\juce_ValueTreeSynchroniser.h">
      <Filter>JUCE Modules\juce_data_structures\values</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_data_structures\juce_data_structures.h">
      <Filter>JUCE Modules\juce_data_structures</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ActionBroadcaster.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ActionListener.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_AsyncUpdater.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ChangeBroadcaster.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_ChangeListener.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\broadcasters\juce_LockingAsyncUpdater.h">
      <Filter>JUCE Modules\juce_events\broadcasters</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_ChildProcessManager.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_ConnectedChildProcess.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnection.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_InterprocessConnectionServer.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\interprocess\juce_NetworkServiceDiscovery.h">
      <Filter>JUCE Modules\juce_events\interprocess</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_ApplicationBase.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_CallbackMessage.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_DeletedAtShutdown.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_Initialisation.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_Message.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_MessageListener.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_MessageManager.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_MountedVolumeListChangeDetector.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\messages\juce_NotificationType.h">
      <Filter>JUCE Modules\juce_events\messages</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_EventLoop_linux.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_EventLoopInternal_linux.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_HiddenMessageWindow_windows.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_MessageQueue_mac.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_RunningInUnity.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_ScopedLowPowerModeDisabler.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\native\juce_WinRTWrapper_windows.h">
      <Filter>JUCE Modules\juce_events\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\timers\juce_MultiTimer.h">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\timers\juce_TimedCallback.h">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\timers\juce_Timer.h">
      <Filter>JUCE Modules\juce_events\timers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_events\juce_events.h">
      <Filter>JUCE Modules\juce_events</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_Colour.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_ColourGradient.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_Colours.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_FillType.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\colour\juce_PixelFormats.h">
      <Filter>JUCE Modules\juce_graphics\colour</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\contexts\juce_GraphicsContext.h">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\contexts\juce_LowLevelGraphicsContext.h">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\contexts\juce_LowLevelGraphicsSoftwareRenderer.h">
      <Filter>JUCE Modules\juce_graphics\contexts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_JustifiedText.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_Ranges.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_ShapedText.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_SimpleShapedText.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\detail\juce_Unicode.h">
      <Filter>JUCE Modules\juce_graphics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\effects\juce_DropShadowEffect.h">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\effects\juce_GlowEffect.h">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\effects\juce_ImageEffectFilter.h">
      <Filter>JUCE Modules\juce_graphics\effects</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT\CBDT.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CBDT</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR\COLR.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR\colrv1-closure.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\COLR</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL\CPAL.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\CPAL</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix\sbix.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\sbix</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Color\svg\svg.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Color\svg</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\composite-iter.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\CompositeGlyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\glyf-helpers.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\glyf.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\Glyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\GlyphHeader.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\loca.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\path-builder.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\SimpleGlyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\glyf\SubsetGlyph.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\glyf</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\Coverage.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\CoverageFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\CoverageFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common\RangeRecord.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\Common</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF\GDEF.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GDEF</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\Anchor.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorFormat3.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\AnchorMatrix.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ChainContextPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\Common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ContextPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\CursivePos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\CursivePosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ExtensionPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\GPOS.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\LigatureArray.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkArray.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkBasePos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkBasePosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkLigPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkLigPosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkMarkPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkMarkPosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\MarkRecord.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairPosFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairSet.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PairValueRecord.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PosLookup.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\PosLookupSubTable.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePosFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\SinglePosFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS\ValueFormat.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GPOS</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSet.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\AlternateSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ChainContextSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ContextSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ExtensionSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\GSUB.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Ligature.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSet.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\LigatureSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\MultipleSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\MultipleSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ReverseChainSingleSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\ReverseChainSingleSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\Sequence.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubst.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubstFormat1.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SingleSubstFormat2.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SubstLookup.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB\SubstLookupSubTable.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout\GSUB</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Layout\types.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\name\name.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\name</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\coord-setter.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC\VARC.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz\OT\Var\VARC</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-ankr-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-bsln-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-feat-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-just-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-kerx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-morx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-opbd-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout-trak-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-layout.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-ltag-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat-map.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-aat.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-algs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-array.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-atomic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bimap.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bit-page.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bit-set-invertible.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-bit-set.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-blob.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-blob.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-json.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-text-glyphs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer-deserialize-text-unicode.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-buffer.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cache.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-cs-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff-interp-dict-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff1-interp-cs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cff2-interp-cs.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-common.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-config.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-coretext.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-cplusplus.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-debug.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-deprecated.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-directwrite.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-dispatch.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-draw.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-draw.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-face.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-font.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-font.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ft-colr.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ft.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gdi.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-geometry.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-glib.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gobject-structs.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-gobject.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-graphite2.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-icu.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-iter.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-kern.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-limits.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-machinery.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-map.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-map.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-meta.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ms-feature-ranges.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-multimap.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-mutex.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-null.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-number-parser.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-number.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-object.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-open-file.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-open-type.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-std-str.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff1-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cff2-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-cmap-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-color.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-deprecated.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-face-table-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-face.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-font.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-gasp-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-glyf-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-hdmx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-head-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-hhea-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-hmtx-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-kern-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-base-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gdef-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gpos-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gsub-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-gsubgpos.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout-jstf-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-layout.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-map.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-math-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-math.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-maxp-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-meta.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-metrics.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-language-static.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-language.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-name.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-os2-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-os2-unicode-ranges.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-macroman.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-table-v2subset.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-post-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-fallback.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape-normalize.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shape.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-fallback.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-joining-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-pua.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic-win1256.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-arabic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-indic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-khmer-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-myanmar-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-syllabic.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use-machine.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-use-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper-vowel-constraints.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-shaper.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-stat-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-tag-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-avar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-cvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-fvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-gvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-hvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-mvar-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var-varc-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-var.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot-vorg-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ot.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-outline.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint-extents.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-paint.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-pool.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-priority-queue.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-repacker.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-sanitize.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-serialize.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set-digest.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-set.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape-plan.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shape.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper-impl.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-shaper.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-string-array.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-style.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-accelerator.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-cff-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-input.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-iup.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-instancer-solver.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan-member-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-plan.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset-repacker.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-subset.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-ucd-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode-emoji-table.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-unicode.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-uniscribe.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-utf.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-vector.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-version.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-blob.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-buffer.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-common.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-face.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-font.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-list.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api-shape.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb-wasm-api.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb.h">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\harfbuzz\hb.hh">
      <Filter>JUCE Modules\juce_graphics\fonts\harfbuzz</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_AttributedString.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_Font.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_FontOptions.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_FunctionPointerDestructor.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_GlyphArrangement.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_LruCache.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_TextLayout.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_Typeface.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\fonts\juce_TypefaceFileCache.h">
      <Filter>JUCE Modules\juce_graphics\fonts</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_AffineTransform.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_BorderSize.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_EdgeTable.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Line.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Parallelogram.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Path.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathIterator.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_PathStrokeType.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Point.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_Rectangle.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\geometry\juce_RectangleList.h">
      <Filter>JUCE Modules\juce_graphics\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\cderror.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jchuff.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jconfig.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdct.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jdhuff.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jerror.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jinclude.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmemsys.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jmorecfg.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jpegint.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jpeglib.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\jversion.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\transupp.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\png.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngconf.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngdebug.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pnginfo.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngpriv.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\pngstruct.h">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_Image.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ImageCache.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ImageConvolutionKernel.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ImageFileFormat.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\images\juce_ScaledImage.h">
      <Filter>JUCE Modules\juce_graphics\images</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_CoreGraphicsContext_mac.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_CoreGraphicsHelpers_mac.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DGraphicsContext_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DHwndContext_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImage_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DImageContext_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DMetrics_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_Direct2DPixelDataPage_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_DirectX_windows.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_EventTracing.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\native\juce_RenderingHelpers.h">
      <Filter>JUCE Modules\juce_graphics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\placement\juce_Justification.h">
      <Filter>JUCE Modules\juce_graphics\placement</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\placement\juce_RectanglePlacement.h">
      <Filter>JUCE Modules\juce_graphics\placement</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBAlgorithm.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBBase.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBBidiType.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBCodepoint.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBCodepointSequence.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBConfig.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBGeneralCategory.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBLine.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBMirrorLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBParagraph.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBRun.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBScript.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SBScriptLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Headers\SheenBidi.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Headers</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiChain.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BidiTypeLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BracketQueue.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\BracketType.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\GeneralCategoryLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\IsolatingRun.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\LevelRun.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\PairingLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunExtrema.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunKind.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\RunQueue.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBAlgorithm.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBAssert.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBBase.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBCodepointSequence.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLine.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBLog.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBMirrorLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBParagraph.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\SBScriptLocator.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptLookup.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\ScriptStack.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\Source\StatusStack.h">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi\Source</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_graphics\juce_graphics.h">
      <Filter>JUCE Modules\juce_graphics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityActions.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\enums</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityEvent.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\enums</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\enums\juce_AccessibilityRole.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\enums</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityCellInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityTableInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityTextInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\interfaces\juce_AccessibilityValueInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility\interfaces</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\juce_AccessibilityHandler.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\accessibility\juce_AccessibilityState.h">
      <Filter>JUCE Modules\juce_gui_basics\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\application\juce_Application.h">
      <Filter>JUCE Modules\juce_gui_basics\application</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ArrowButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_Button.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_DrawableButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_HyperlinkButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ImageButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ShapeButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_TextButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToggleButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\buttons\juce_ToolbarButton.h">
      <Filter>JUCE Modules\juce_gui_basics\buttons</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandID.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandInfo.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandManager.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_ApplicationCommandTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\commands\juce_KeyPressMappingSet.h">
      <Filter>JUCE Modules\juce_gui_basics\commands</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_CachedComponentImage.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_Component.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_ComponentListener.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_ComponentTraverser.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_FocusTraverser.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\components\juce_ModalComponentManager.h">
      <Filter>JUCE Modules\juce_gui_basics\components</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Desktop.h">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\desktop\juce_Displays.h">
      <Filter>JUCE Modules\juce_gui_basics\desktop</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_AccessibilityHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_AlertWindowHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ButtonAccessibilityHandler.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ComponentHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ComponentPeerHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_CustomMouseCursorInfo.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_FocusHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_FocusRestorer.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_LookAndFeelHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_MouseInputSourceImpl.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_MouseInputSourceList.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_PointerState.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScalingHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedContentSharerImpl.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedContentSharerInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedMessageBoxImpl.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ScopedMessageBoxInterface.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_StandardCachedComponentImage.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ToolbarItemDragAndDropOverlayComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_TopLevelWindowManager.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_ViewportHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\detail\juce_WindowingHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_Drawable.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableComposite.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableImage.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawablePath.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableRectangle.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableShape.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\drawables\juce_DrawableText.h">
      <Filter>JUCE Modules\juce_gui_basics\drawables</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ContentSharer.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsDisplayComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_DirectoryContentsList.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileBrowserComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileBrowserListener.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooser.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileChooserDialogBox.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileListComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FilenameComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FilePreviewComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileSearchPathListComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_FileTreeComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\filebrowser\juce_ImagePreviewComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\filebrowser</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_CaretComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyboardFocusTraverser.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyListener.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_KeyPress.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_ModifierKeys.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_SystemClipboard.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_TextEditorKeyMapper.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\keyboard\juce_TextInputTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\keyboard</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_AnimatedPosition.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_AnimatedPositionBehaviours.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_BorderedComponentBoundsConstrainer.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentAnimator.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBoundsConstrainer.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentBuilder.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ComponentMovementWatcher.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ConcertinaPanel.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_FlexBox.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_FlexItem.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Grid.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GridItem.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_GroupComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_MultiDocumentPanel.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableBorderComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableCornerComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ResizableEdgeComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_ScrollBar.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_SidePanel.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutManager.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableLayoutResizerBar.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_StretchableObjectResizer.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedButtonBar.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_TabbedComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\layout\juce_Viewport.h">
      <Filter>JUCE Modules\juce_gui_basics\layout</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V1.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V2.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V3.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\lookandfeel\juce_LookAndFeel_V4.h">
      <Filter>JUCE Modules\juce_gui_basics\lookandfeel</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_BurgerMenuComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_MenuBarModel.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\menus\juce_PopupMenu.h">
      <Filter>JUCE Modules\juce_gui_basics\menus</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\misc\juce_BubbleComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\misc\juce_DropShadower.h">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\misc\juce_FocusOutline.h">
      <Filter>JUCE Modules\juce_gui_basics\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_ComponentDragger.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_DragAndDropContainer.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_DragAndDropTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_FileDragAndDropTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_LassoComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseCursor.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseEvent.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInactivityDetector.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseInputSource.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_MouseListener.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_SelectedItemSet.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_TextDragAndDropTarget.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\mouse\juce_TooltipClient.h">
      <Filter>JUCE Modules\juce_gui_basics\mouse</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityElement_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_AccessibilityTextHelpers.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAExpandCollapseProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAGridItemProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAGridProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAHelpers_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAInvokeProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAProviderBase_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAProviders_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIARangeValueProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIASelectionProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIATextProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAToggleProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIATransformProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAValueProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_UIAWindowProvider_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\accessibility\juce_WindowsUIAWrapper_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native\accessibility</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_CGMetalLayerRenderer_mac.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_MultiTouchMapper.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_NativeModalWrapperComponent_ios.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_PerScreenDisplayLinks_mac.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedDPIAwarenessDisabler.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedThreadDPIAwarenessSetter_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_ScopedWindowAssociation_linux.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_WindowsHooks_windows.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_XSymbols_linux.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\native\juce_XWindowSystem_linux.h">
      <Filter>JUCE Modules\juce_gui_basics\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_MarkerList.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinate.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeCoordinatePositioner.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeParallelogram.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePoint.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativePointPath.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\positioning\juce_RelativeRectangle.h">
      <Filter>JUCE Modules\juce_gui_basics\positioning</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_BooleanPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ButtonPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_ChoicePropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_MultiChoicePropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_PropertyPanel.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_SliderPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\properties\juce_TextPropertyComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\properties</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ComboBox.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ImageComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Label.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ListBox.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ProgressBar.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Slider.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableHeaderComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TableListBox.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TextEditor.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_Toolbar.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemComponent.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemFactory.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_ToolbarItemPalette.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\widgets\juce_TreeView.h">
      <Filter>JUCE Modules\juce_gui_basics\widgets</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_AlertWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_CallOutBox.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ComponentPeer.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DialogWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_DocumentWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_MessageBoxOptions.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeMessageBox.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_NativeScaleFactorNotifier.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ResizableWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ScopedMessageBox.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_ThreadWithProgressWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TooltipWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_TopLevelWindow.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_VBlankAttachment.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\windows\juce_WindowUtils.h">
      <Filter>JUCE Modules\juce_gui_basics\windows</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_basics\juce_gui_basics.h">
      <Filter>JUCE Modules\juce_gui_basics</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeDocument.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeEditorComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_CPlusPlusCodeTokeniserFunctions.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_LuaCodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\code_editor\juce_XMLCodeTokeniser.h">
      <Filter>JUCE Modules\juce_gui_extra\code_editor</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\detail\juce_WebControlRelayEvents.h">
      <Filter>JUCE Modules\juce_gui_extra\detail</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\documents\juce_FileBasedDocument.h">
      <Filter>JUCE Modules\juce_gui_extra\documents</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_ActiveXControlComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_AndroidViewComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_HWNDComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_NSViewComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_UIViewComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\embedding\juce_XEmbedComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\embedding</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_AnimatedAppComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_AppleRemote.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_BubbleMessageComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_ColourSelector.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_KeyMappingEditorComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_LiveConstantEditor.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PreferencesPanel.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_PushNotifications.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_RecentlyOpenedFilesList.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SplashScreen.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_SystemTrayIconComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebBrowserComponent.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebControlParameterIndexReceiver.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\misc\juce_WebControlRelays.h">
      <Filter>JUCE Modules\juce_gui_extra\misc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\native\juce_NSViewFrameWatcher_mac.h">
      <Filter>JUCE Modules\juce_gui_extra\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_gui_extra\juce_gui_extra.h">
      <Filter>JUCE Modules\juce_gui_extra</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\geometry\juce_Draggable3DOrientation.h">
      <Filter>JUCE Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\geometry\juce_Matrix3D.h">
      <Filter>JUCE Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\geometry\juce_Quaternion.h">
      <Filter>JUCE Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\geometry\juce_Vector3D.h">
      <Filter>JUCE Modules\juce_opengl\geometry</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\native\juce_OpenGL_android.h">
      <Filter>JUCE Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\native\juce_OpenGL_ios.h">
      <Filter>JUCE Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\native\juce_OpenGL_linux.h">
      <Filter>JUCE Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\native\juce_OpenGL_mac.h">
      <Filter>JUCE Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\native\juce_OpenGL_windows.h">
      <Filter>JUCE Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\native\juce_OpenGLExtensions.h">
      <Filter>JUCE Modules\juce_opengl\native</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_gl.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_gles2.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_khrplatform.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLContext.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLFrameBuffer.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLGraphicsContext.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLHelpers.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLImage.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLPixelFormat.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLRenderer.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLShaderProgram.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_OpenGLTexture.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\opengl\juce_wgl.h">
      <Filter>JUCE Modules\juce_opengl\opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\utils\juce_OpenGLAppComponent.h">
      <Filter>JUCE Modules\juce_opengl\utils</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_opengl\juce_opengl.h">
      <Filter>JUCE Modules\juce_opengl</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCAddress.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCArgument.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCBundle.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCMessage.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCReceiver.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCSender.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCTimeTag.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\osc\juce_OSCTypes.h">
      <Filter>JUCE Modules\juce_osc\osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\..\..\modules\juce_osc\juce_osc.h">
      <Filter>JUCE Modules\juce_osc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\JuceLibraryCode\BinaryData.h">
      <Filter>JUCE Library Code</Filter>
    </ClInclude>
    <ClInclude Include="..\..\JuceLibraryCode\JuceHeader.h">
      <Filter>JUCE Library Code</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\Source\juce_icon.png">
      <Filter>NetworkGraphicsDemo</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\common\README.md">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\common</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_devices\native\oboe\src\flowgraph\resampler\README.md">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe\src\flowgraph\resampler</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_devices\native\oboe\CMakeLists.txt">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_devices\native\oboe\README.md">
      <Filter>JUCE Modules\juce_audio_devices\native\oboe</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\Flac Licence.txt">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_formats\codecs\flac\JUCE_CHANGES.txt">
      <Filter>JUCE Modules\juce_audio_formats\codecs\flac</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7\README.md">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis\libvorbis-1.3.7</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_formats\codecs\oggvorbis\Ogg Vorbis Licence.txt">
      <Filter>JUCE Modules\juce_audio_formats\codecs\oggvorbis</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\LV2_SDK\README.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\LV2_SDK</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\LICENSE.txt">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\base\README.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\base</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\LICENSE.txt">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces\README.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\pluginterfaces</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo\ReadMe.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\source\vst\moduleinfo</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\LICENSE.txt">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\public.sdk\README.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK\public.sdk</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\JUCE_README.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\LICENSE.txt">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_audio_processors\format_types\VST3_SDK\README.md">
      <Filter>JUCE Modules\juce_audio_processors\format_types\VST3_SDK</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_core\native\java\README.txt">
      <Filter>JUCE Modules\juce_core\native\java</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_core\zip\zlib\JUCE_CHANGES.txt">
      <Filter>JUCE Modules\juce_core\zip\zlib</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_graphics\image_formats\jpglib\changes to libjpeg for JUCE.txt">
      <Filter>JUCE Modules\juce_graphics\image_formats\jpglib</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_graphics\image_formats\pnglib\libpng_readme.txt">
      <Filter>JUCE Modules\juce_graphics\image_formats\pnglib</Filter>
    </None>
    <None Include="..\..\..\..\modules\juce_graphics\unicode\sheenbidi\JUCE_CHANGES.txt">
      <Filter>JUCE Modules\juce_graphics\unicode\sheenbidi</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include=".\resources.rc">
      <Filter>JUCE Library Code</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>
