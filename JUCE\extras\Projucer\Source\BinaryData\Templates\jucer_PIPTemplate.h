/*******************************************************************************
 The block below describes the properties of this PIP. A PIP is a short snippet
 of code that can be read by the Projucer and used to generate a JUCE project.

 BEGIN_JUCE_PIP_METADATA

%%pip_metadata%%

 END_JUCE_PIP_METADATA

*******************************************************************************/

#pragma once


//==============================================================================
%%pip_code%%
