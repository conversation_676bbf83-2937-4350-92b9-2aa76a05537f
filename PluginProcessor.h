#pragma once

#include <juce_audio_processors/juce_audio_processors.h>
#include <juce_dsp/juce_dsp.h>
#include <juce_audio_devices/juce_audio_devices.h>

//==============================================================================
class CompliAudioProcessor final : public juce::AudioProcessor
{
public:
    //==============================================================================
    CompliAudioProcessor();
    ~CompliAudioProcessor() override;

    //==============================================================================
    void prepareToPlay (double sampleRate, int samplesPerBlock) override;
    void releaseResources() override;

    bool isBusesLayoutSupported (const BusesLayout& layouts) const override;

    void processBlock (juce::AudioBuffer<float>&, juce::MidiBuffer&) override;
    using AudioProcessor::processBlock;

    //==============================================================================
    juce::AudioProcessorEditor* createEditor() override;
    bool hasEditor() const override;

    //==============================================================================
    const juce::String getName() const override;

    bool acceptsMidi() const override;
    bool producesMidi() const override;
    bool isMidiEffect() const override;
    double getTailLengthSeconds() const override;

    //==============================================================================
    int getNumPrograms() override;
    int getCurrentProgram() override;
    void setCurrentProgram (int index) override;
    const juce::String getProgramName (int index) override;
    void changeProgramName (int index, const juce::String& newName) override;

    //==============================================================================
    void getStateInformation (juce::MemoryBlock& destData) override;
    void setStateInformation (const void* data, int sizeInBytes) override;

    static juce::AudioProcessorValueTreeState::ParameterLayout createParameterLayout();
    juce::AudioProcessorValueTreeState apvts {*this, nullptr, "Parameters", createParameterLayout()};

    // Level meters
    std::atomic<float> inputLevel{0.0f};
    std::atomic<float> outputLevel{0.0f};
    std::atomic<float> gainReduction{0.0f};

    // Preset management
    void loadPreset(int presetIndex);
    void updatePresetParameters();

    // Audio device management (for standalone)
    juce::AudioDeviceManager& getAudioDeviceManager() { return audioDeviceManager; }
    juce::StringArray getAvailableInputDevices();
    void setInputDevice(const juce::String& deviceName);

private:
    //==============================================================================
    // Audio device management
    juce::AudioDeviceManager audioDeviceManager;

    // DSP processing chain
    juce::dsp::ProcessorChain<juce::dsp::Compressor<float>, juce::dsp::Limiter<float>> processingChain;

    // Preset definitions
    struct PresetData
    {
        float threshold, ratio, attack, release, makeupGain;
        float limiterThreshold;
        juce::String name;
    };

    static const std::array<PresetData, 5> presets;
    int currentPresetIndex = 0;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR (CompliAudioProcessor)
};
