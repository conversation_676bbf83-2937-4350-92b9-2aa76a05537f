# ==============================================================================
#
#  This file is part of the JUCE library.
#  Copyright (c) - Raw Material Software Limited
#
#  JUCE is an open source library subject to commercial or open-source
#  licensing.
#
#  The code included in this file is provided under the terms of the ISC license
#  http://www.isc.org/downloads/software-support-policy/isc-license. Permission
#  To use, copy, modify, and/or distribute this software for any purpose with or
#  without fee is hereby granted provided that the above copyright notice and
#  this permission notice appear in all copies.
#
#  JUCE IS PROVIDED "AS IS" WITHOUT ANY WARRANTY, AND ALL WARRANTIES, WHETHER
#  EXPRESSED OR IMPLIED, INCLUDING MERCHANTABILITY AND FITNESS FOR PURPOSE, ARE
#  DISCLAIMED.
#
# ==============================================================================

@PACKAGE_INIT@

include("${CMAKE_CURRENT_LIST_DIR}/LV2_HELPER.cmake")
include("${CMAKE_CURRENT_LIST_DIR}/VST3_HELPER.cmake")

if(NOT TARGET juce::juceaide)
    add_executable(juce::juceaide IMPORTED)
    set_target_properties(juce::juceaide PROPERTIES
        IMPORTED_LOCATION "@PACKAGE_JUCEAIDE_PATH@")
endif()

check_required_components("@PROJECT_NAME@")

set(JUCE_MODULES_DIR "@PACKAGE_JUCE_MODULE_PATH@" CACHE INTERNAL
    "The path to JUCE modules")

include("@PACKAGE_UTILS_INSTALL_DIR@/JUCEModuleSupport.cmake")
include("@PACKAGE_UTILS_INSTALL_DIR@/JUCEUtils.cmake")

set(_juce_modules
    juce_analytics
    juce_animation
    juce_audio_basics
    juce_audio_devices
    juce_audio_formats
    juce_audio_plugin_client
    juce_audio_processors
    juce_audio_utils
    juce_box2d
    juce_core
    juce_cryptography
    juce_data_structures
    juce_dsp
    juce_events
    juce_graphics
    juce_gui_basics
    juce_gui_extra
    juce_javascript
    juce_midi_ci
    juce_opengl
    juce_osc
    juce_product_unlocking
    juce_video)

set(_targets_defined)
set(_targets_expected)

foreach(_juce_module IN LISTS _juce_modules)
    list(APPEND _targets_expected ${_juce_module} juce::${_juce_modules})
    if(TARGET ${_juce_module})
        list(APPEND _targets_defined ${_juce_module})
    endif()

    if(TARGET juce::${_juce_module})
        list(APPEND _targets_defined juce::${_juce_module})
    endif()
endforeach()

if("${_targets_defined}" STREQUAL "${_targets_expected}")
    unset(_targets_defined)
    unset(_targets_expected)
    return()
endif()

if(NOT "${_targets_defined}" STREQUAL "")
    message(FATAL_ERROR "Some targets in this export set were already defined.")
endif()

unset(_targets_defined)
unset(_targets_expected)

foreach(_juce_module IN LISTS _juce_modules)
    juce_add_module("@PACKAGE_JUCE_MODULE_PATH@/${_juce_module}" ALIAS_NAMESPACE juce)
endforeach()

unset(_juce_modules)
