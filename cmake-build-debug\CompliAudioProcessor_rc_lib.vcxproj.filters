﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CMakeFiles\628366c1adb751f267828c1bc262ae01\CompliAudioProcessor_resources.rc.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Documents\GitHub\compli2\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="C:\Users\<USER>\Documents\GitHub\compli2\cmake-build-debug\CompliAudioProcessor_artefacts\JuceLibraryCode\CompliAudioProcessor_resources.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{60D997CF-72EB-38A1-AB70-DC20D18EC709}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{F429F641-4117-3237-83F1-8306F1D0558E}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
