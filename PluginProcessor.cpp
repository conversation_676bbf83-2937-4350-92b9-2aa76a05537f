#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
// Preset definitions based on specifications
const std::array<CompliAudioProcessor::PresetData, 5> CompliAudioProcessor::presets = {{
    // Voice Optimized (Default): Ratio 3:1, GR 4-5dB, Attack 5-10ms, Release 50-60ms, Makeup 2-3dB, Limiter -3dB
    {-18.0f, 3.0f, 8.0f, 55.0f, 2.5f, -3.0f, "Voice Optimized"},

    // Broadcast: Ratio 4:1-6:1, GR 6-8dB, Attack 3-5ms, Release 50-100ms, Limiter -1 to -2dB
    {-20.0f, 5.0f, 4.0f, 75.0f, 6.0f, -1.5f, "Broadcast"},

    // Podcast: -16 LUFS target, Ratio 3:1-4:1, GR 3-5dB, Attack 5-8ms, Release 40-50ms, Limiter -3 to -6dB
    {-16.0f, 3.5f, 6.5f, 45.0f, 4.0f, -4.5f, "Podcast"},

    // Gaming: Fast response, Ratio 4:1-6:1, GR 3-6dB, Attack 1-3ms, Release 30-50ms
    {-15.0f, 5.0f, 2.0f, 40.0f, 4.5f, -2.0f, "Gaming"},

    // Custom: User-defined settings (starting with Voice Optimized as base)
    {-18.0f, 3.0f, 8.0f, 55.0f, 2.5f, -3.0f, "Custom"}
}};

CompliAudioProcessor::CompliAudioProcessor()
     : AudioProcessor (BusesProperties()
                     #if ! JucePlugin_IsMidiEffect
                      #if ! JucePlugin_IsSynth
                       .withInput  ("Input",  juce::AudioChannelSet::stereo(), true)
                      #endif
                       .withOutput ("Output", juce::AudioChannelSet::stereo(), true)
                     #endif
                       )
{
}

CompliAudioProcessor::~CompliAudioProcessor()
{
}

//==============================================================================
const juce::String CompliAudioProcessor::getName() const
{
    return JucePlugin_Name;
}

bool CompliAudioProcessor::acceptsMidi() const
{
   #if JucePlugin_WantsMidiInput
    return true;
   #else
    return false;
   #endif
}

bool CompliAudioProcessor::producesMidi() const
{
   #if JucePlugin_ProducesMidiOutput
    return true;
   #else
    return false;
   #endif
}

bool CompliAudioProcessor::isMidiEffect() const
{
   #if JucePlugin_IsMidiEffect
    return true;
   #else
    return false;
   #endif
}

double CompliAudioProcessor::getTailLengthSeconds() const
{
    return 0.0;
}

int CompliAudioProcessor::getNumPrograms()
{
    return 1;   // NB: some hosts don't cope very well if you tell them there are 0 programs,
                // so this should be at least 1, even if you're not really implementing programs.
}

int CompliAudioProcessor::getCurrentProgram()
{
    return 0;
}

void CompliAudioProcessor::setCurrentProgram (int index)
{
    juce::ignoreUnused (index);
}

const juce::String CompliAudioProcessor::getProgramName (int index)
{
    juce::ignoreUnused (index);
    return {};
}

void CompliAudioProcessor::changeProgramName (int index, const juce::String& newName)
{
    juce::ignoreUnused (index, newName);
}

//==============================================================================
void CompliAudioProcessor::prepareToPlay (double sampleRate, int samplesPerBlock)
{
    // Use this method as the place to do any pre-playback
    // initialisation that you need..
    juce::dsp::ProcessSpec spec;
    spec.sampleRate = sampleRate;
    spec.maximumBlockSize = static_cast<juce::uint32>(samplesPerBlock);
    spec.numChannels = static_cast<juce::uint32>(getTotalNumOutputChannels());

    processingChain.prepare(spec);

    // Initialize compressor with default settings
    auto& compressor = processingChain.get<0>();
    compressor.setThreshold(-18.0f);
    compressor.setRatio(3.0f);
    compressor.setAttack(8.0f);
    compressor.setRelease(55.0f);

    // Initialize limiter with default settings
    auto& limiter = processingChain.get<1>();
    limiter.setThreshold(-0.5f);
    limiter.setRelease(50.0f);
}

void CompliAudioProcessor::releaseResources()
{
    // When playback stops, you can use this as an opportunity to free up any
    // spare memory, etc.
    processingChain.reset();
}

bool CompliAudioProcessor::isBusesLayoutSupported (const BusesLayout& layouts) const
{
  #if JucePlugin_IsMidiEffect
    juce::ignoreUnused (layouts);
    return true;
  #else
    // This is the place where you check if the layout is supported.
    // In this template code we only support mono or stereo.
    // Some plugin hosts, such as certain GarageBand versions, will only
    // load plugins that support stereo bus layouts.
    if (layouts.getMainOutputChannelSet() != juce::AudioChannelSet::mono()
     && layouts.getMainOutputChannelSet() != juce::AudioChannelSet::stereo())
        return false;

    // This checks if the input layout matches the output layout
   #if ! JucePlugin_IsSynth
    if (layouts.getMainOutputChannelSet() != layouts.getMainInputChannelSet())
        return false;
   #endif

    return true;
  #endif
}

void CompliAudioProcessor::processBlock (juce::AudioBuffer<float>& buffer,
                                              juce::MidiBuffer& midiMessages)
{
    juce::ignoreUnused (midiMessages);

    juce::ScopedNoDenormals noDenormals;
    auto totalNumInputChannels  = getTotalNumInputChannels();
    auto totalNumOutputChannels = getTotalNumOutputChannels();

    // In case we have more outputs than inputs, this code clears any output
    // channels that didn't contain input data, (because these aren't
    // guaranteed to be empty - they may contain garbage).
    for (auto i = totalNumInputChannels; i < totalNumOutputChannels; ++i)
        buffer.clear (i, 0, buffer.getNumSamples());

    // Calculate input level for metering
    float inputRMS = 0.0f;
    for (int channel = 0; channel < totalNumInputChannels; ++channel)
    {
        auto* channelData = buffer.getReadPointer(channel);
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            inputRMS += channelData[sample] * channelData[sample];
        }
    }
    inputRMS = std::sqrt(inputRMS / (buffer.getNumSamples() * totalNumInputChannels));
    inputLevel.store(juce::Decibels::gainToDecibels(inputRMS, -60.0f));

    // Check for preset changes
    int newPresetIndex = static_cast<int>(apvts.getRawParameterValue("preset")->load());
    if (newPresetIndex != currentPresetIndex && newPresetIndex < 4) // Don't auto-load Custom preset
    {
        currentPresetIndex = newPresetIndex;
        loadPreset(currentPresetIndex);
    }

    // Update compressor parameters from APVTS
    auto& compressor = processingChain.get<0>();
    compressor.setThreshold(apvts.getRawParameterValue("threshold")->load());
    compressor.setRatio(apvts.getRawParameterValue("ratio")->load());
    compressor.setAttack(apvts.getRawParameterValue("attack")->load());
    compressor.setRelease(apvts.getRawParameterValue("release")->load());

    // Update limiter parameters from APVTS
    auto& limiter = processingChain.get<1>();
    limiter.setThreshold(apvts.getRawParameterValue("limiterThreshold")->load());

    // Check bypass state
    bool isBypassed = apvts.getRawParameterValue("bypass")->load() > 0.5f;
    bool limiterEnabled = apvts.getRawParameterValue("limiterEnabled")->load() > 0.5f;

    if (!isBypassed)
    {
        // Process audio through the chain
        juce::dsp::AudioBlock<float> block(buffer);
        juce::dsp::ProcessContextReplacing<float> context(block);

        // Process compressor
        auto compressorBlock = block;
        juce::dsp::ProcessContextReplacing<float> compressorContext(compressorBlock);
        processingChain.get<0>().process(compressorContext);

        // Apply makeup gain
        float makeupGain = juce::Decibels::decibelsToGain(apvts.getRawParameterValue("makeupGain")->load());
        buffer.applyGain(makeupGain);

        // Process limiter if enabled
        if (limiterEnabled)
        {
            auto limiterBlock = juce::dsp::AudioBlock<float>(buffer);
            juce::dsp::ProcessContextReplacing<float> limiterContext(limiterBlock);
            processingChain.get<1>().process(limiterContext);
        }
    }

    // Calculate output level and gain reduction for metering
    float outputRMS = 0.0f;
    for (int channel = 0; channel < totalNumInputChannels; ++channel)
    {
        auto* channelData = buffer.getReadPointer(channel);
        for (int sample = 0; sample < buffer.getNumSamples(); ++sample)
        {
            outputRMS += channelData[sample] * channelData[sample];
        }
    }
    outputRMS = std::sqrt(outputRMS / (buffer.getNumSamples() * totalNumInputChannels));
    outputLevel.store(juce::Decibels::gainToDecibels(outputRMS, -60.0f));

    // Calculate gain reduction (simplified)
    float makeupGainDb = apvts.getRawParameterValue("makeupGain")->load();
    float grValue = inputLevel.load() - outputLevel.load() + makeupGainDb;
    gainReduction.store(std::max(0.0f, grValue));
}

//==============================================================================
bool CompliAudioProcessor::hasEditor() const
{
    return true; // (change this to false if you choose to not supply an editor)
}

juce::AudioProcessorEditor* CompliAudioProcessor::createEditor()
{
    return new CompliAudioProcessorEditor (*this);
}

//==============================================================================
void CompliAudioProcessor::getStateInformation (juce::MemoryBlock& destData)
{
    // You should use this method to store your parameters in the memory block.
    auto state = apvts.copyState();
    std::unique_ptr<juce::XmlElement> xml (state.createXml());
    copyXmlToBinary (*xml, destData);
}

void CompliAudioProcessor::setStateInformation (const void* data, int sizeInBytes)
{
    // You should use this method to restore your parameters from this memory block,
    std::unique_ptr<juce::XmlElement> xmlState (getXmlFromBinary (data, sizeInBytes));
    if (xmlState.get() != nullptr)
        if (xmlState->hasTagName (apvts.state.getType()))
            apvts.replaceState (juce::ValueTree::fromXml (*xmlState));
}

juce::AudioProcessorValueTreeState::ParameterLayout CompliAudioProcessor::createParameterLayout()
{
    juce::AudioProcessorValueTreeState::ParameterLayout layout;

    // Compressor parameters - Extended ranges for Custom preset
    layout.add(std::make_unique<juce::AudioParameterFloat>("threshold", "Threshold",
        juce::NormalisableRange<float>(-60.0f, 0.0f, 0.1f, 1.0f), -18.0f));

    // Ratio: 1:1 to 20:1 as specified for Custom
    layout.add(std::make_unique<juce::AudioParameterFloat>("ratio", "Ratio",
        juce::NormalisableRange<float>(1.0f, 20.0f, 0.1f, 0.5f), 3.0f));

    // Attack: Extended range 0.1ms - several seconds (3000ms = 3 sec)
    layout.add(std::make_unique<juce::AudioParameterFloat>("attack", "Attack",
        juce::NormalisableRange<float>(0.1f, 3000.0f, 0.1f, 0.3f), 8.0f));

    // Release: Extended range 1ms - several seconds (5000ms = 5 sec)
    layout.add(std::make_unique<juce::AudioParameterFloat>("release", "Release",
        juce::NormalisableRange<float>(1.0f, 5000.0f, 1.0f, 0.3f), 55.0f));

    // Makeup Gain: Extended range for fine adjustment
    layout.add(std::make_unique<juce::AudioParameterFloat>("makeupGain", "Makeup Gain",
        juce::NormalisableRange<float>(-24.0f, 24.0f, 0.1f, 1.0f), 2.5f));

    // Limiter parameters - Full range, fine adjust
    layout.add(std::make_unique<juce::AudioParameterFloat>("limiterThreshold", "Limiter Threshold",
        juce::NormalisableRange<float>(-24.0f, 0.0f, 0.1f, 1.0f), -3.0f));

    layout.add(std::make_unique<juce::AudioParameterBool>("limiterEnabled", "Limiter Enabled", true));

    // Preset selection
    juce::StringArray presetChoices = {"Voice Optimized", "Broadcast", "Podcast", "Gaming", "Custom"};
    layout.add(std::make_unique<juce::AudioParameterChoice>("preset", "Preset", presetChoices, 0));

    // Bypass
    layout.add(std::make_unique<juce::AudioParameterBool>("bypass", "Bypass", false));

    return layout;
}


void CompliAudioProcessor::loadPreset(int presetIndex)
{
    if (presetIndex >= 0 && presetIndex < static_cast<int>(presets.size()))
    {
        const auto& preset = presets[presetIndex];

        // Update APVTS parameters
        apvts.getParameter("threshold")->setValueNotifyingHost(
            apvts.getParameter("threshold")->convertTo0to1(preset.threshold));
        apvts.getParameter("ratio")->setValueNotifyingHost(
            apvts.getParameter("ratio")->convertTo0to1(preset.ratio));
        apvts.getParameter("attack")->setValueNotifyingHost(
            apvts.getParameter("attack")->convertTo0to1(preset.attack));
        apvts.getParameter("release")->setValueNotifyingHost(
            apvts.getParameter("release")->convertTo0to1(preset.release));
        apvts.getParameter("makeupGain")->setValueNotifyingHost(
            apvts.getParameter("makeupGain")->convertTo0to1(preset.makeupGain));
        apvts.getParameter("limiterThreshold")->setValueNotifyingHost(
            apvts.getParameter("limiterThreshold")->convertTo0to1(preset.limiterThreshold));
    }
}

void CompliAudioProcessor::updatePresetParameters()
{
    // This method can be called when parameters change to update the preset selection
    // If parameters don't match any preset exactly, switch to "Custom"
    bool matchesPreset = false;

    for (int i = 0; i < 4; ++i) // Check first 4 presets (excluding Custom)
    {
        const auto& preset = presets[i];

        if (std::abs(apvts.getRawParameterValue("threshold")->load() - preset.threshold) < 0.1f &&
            std::abs(apvts.getRawParameterValue("ratio")->load() - preset.ratio) < 0.1f &&
            std::abs(apvts.getRawParameterValue("attack")->load() - preset.attack) < 0.1f &&
            std::abs(apvts.getRawParameterValue("release")->load() - preset.release) < 0.1f &&
            std::abs(apvts.getRawParameterValue("makeupGain")->load() - preset.makeupGain) < 0.1f &&
            std::abs(apvts.getRawParameterValue("limiterThreshold")->load() - preset.limiterThreshold) < 0.1f)
        {
            matchesPreset = true;
            currentPresetIndex = i;
            break;
        }
    }

    if (!matchesPreset)
    {
        currentPresetIndex = 4; // Custom
        apvts.getParameter("preset")->setValueNotifyingHost(
            apvts.getParameter("preset")->convertTo0to1(4.0f));
    }
}

//==============================================================================
// This creates new instances of the plugin..
juce::AudioProcessor* JUCE_CALLTYPE createPluginFilter()
{
    return new CompliAudioProcessor();
}
