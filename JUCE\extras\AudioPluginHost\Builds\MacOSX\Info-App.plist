<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist>
  <dict>
    <key>NSMicrophoneUsageDescription</key>
    <string>This app requires audio input. If you do not have an audio interface connected it will use the built-in microphone.</string>
    <key>NSAppleEventsUsageDescription</key>
    <string>This is required for some third-party plug-ins to function correctly.</string>
    <key>CFBundleExecutable</key>
    <string>${EXECUTABLE_NAME}</string>
    <key>CFBundleIconFile</key>
    <string>Icon.icns</string>
    <key>CFBundleIdentifier</key>
    <string>com.juce.audiopluginhost</string>
    <key>CFBundleName</key>
    <string>AudioPluginHost</string>
    <key>CFBundleDisplayName</key>
    <string>AudioPluginHost</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>????</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>NSHumanReadableCopyright</key>
    <string>Raw Material Software Limited</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.developer-tools</string>
  </dict>
</plist>
