// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		0140787C0118A95E37DE90B4 /* include_juce_video.mm */ = {isa = PBXBuildFile; fileRef = 9144821E003E15E4042B57DB; };
		0B0CE6D5062E5C02A41F24BC /* CoreText.framework */ = {isa = PBXBuildFile; fileRef = 873F9DD54978E601102353B4; };
		10D769051F1431A67AD2CB40 /* MetalKit.framework */ = {isa = PBXBuildFile; fileRef = 2992DB69DCFB7DADDE907385; settings = { ATTRIBUTES = (Weak, ); }; };
		11D7AB57EC28DB48A066F7AD /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXBuildFile; fileRef = 483C3A8B4FB98B54BB42CB50; };
		1351A13E78F38741C6075600 /* CoreAudio.framework */ = {isa = PBXBuildFile; fileRef = 4F0A137A4115946A346180E6; };
		163B0CF2DD0990A63DF1D5A6 /* AudioToolbox.framework */ = {isa = PBXBuildFile; fileRef = 470C3E4553B513FFEF752779; };
		1BA36E8CA4B9E8D3F3B57D24 /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXBuildFile; fileRef = CCC8AEBCE55002F970222BAD; };
		1C9AA8660D0DE180064ACF7B /* LaunchScreen.storyboard */ = {isa = PBXBuildFile; fileRef = 7FBD555B32197FEACDFF5AFB; };
		1DBDFEDE359CFC84F8E3DE4C /* include_juce_events.mm */ = {isa = PBXBuildFile; fileRef = FCD0D758C5767944BEC29730; };
		26652AB1BB77C8A39434775F /* include_juce_audio_formats.mm */ = {isa = PBXBuildFile; fileRef = E061A1C75FA5722167FC4997; };
		2707968B431D83AC7E28E49B /* include_juce_audio_processors.mm */ = {isa = PBXBuildFile; fileRef = E67AB94002886AF67437D6AE; };
		2F76CA28C8C0EFC7453D0EB8 /* include_juce_data_structures.mm */ = {isa = PBXBuildFile; fileRef = F5F2EA2238973488632FC322; };
		34A4931AF1DD424D3A400EEF /* CoreGraphics.framework */ = {isa = PBXBuildFile; fileRef = 76A157A111866670A4678F04; };
		36E115D98311F12AA06710E6 /* DemoPIPs2.cpp */ = {isa = PBXBuildFile; fileRef = 061AECBF1CC7056F4155812D; };
		41AE56F8671DFE80720A6A18 /* UniformTypeIdentifiers.framework */ = {isa = PBXBuildFile; fileRef = AAF88452B7774FB605990B31; settings = { ATTRIBUTES = (Weak, ); }; };
		46071CE2B98B562B7BF27CB1 /* CoreMedia.framework */ = {isa = PBXBuildFile; fileRef = 1CFE3935A3B810D5D68A2504; };
		47ED2C78B05B8A6A00E36C46 /* Assets */ = {isa = PBXBuildFile; fileRef = 685A261BE78585293F3EAD36; };
		48CF0B02E1D06E5DA51E6270 /* Accelerate.framework */ = {isa = PBXBuildFile; fileRef = A04E4408525F24F7DCBA000E; };
		4FBBB55F4E347757F74F1F41 /* MainComponent.cpp */ = {isa = PBXBuildFile; fileRef = 25E5ED33876A2C752378C859; };
		527DA2E6827BAFDDD3E8E80F /* CoreAudioKit.framework */ = {isa = PBXBuildFile; fileRef = B4389672DA4CC8E0A531062D; };
		55F120E70543228568573D4A /* include_juce_animation.cpp */ = {isa = PBXBuildFile; fileRef = CB4C64BACC1CA4AAD6D69565; };
		5CB78489F16E82144914972D /* include_juce_gui_extra.mm */ = {isa = PBXBuildFile; fileRef = 979F23EA9E5E76131299E886; };
		5E4310B3F6BB639875D3E9B8 /* Foundation.framework */ = {isa = PBXBuildFile; fileRef = 49ECA8B998B339A083674A22; };
		5EB6872A39122A5AB67E544E /* include_juce_audio_processors_ara.cpp */ = {isa = PBXBuildFile; fileRef = 8D44097417573B38729A0179; };
		611298FAC1A543BDD10D4C41 /* include_juce_box2d.cpp */ = {isa = PBXBuildFile; fileRef = 4DF215D350FFE5E119CBA7E5; };
		63A2F309E55DAC206E9B97E3 /* App */ = {isa = PBXBuildFile; fileRef = CFF2BBEB242CC8B3B904B5F9; };
		6658EEC5F9D63D3419EB7098 /* CoreServices.framework */ = {isa = PBXBuildFile; fileRef = E07FC48041C3E9F9721F3BCE; };
		67D7E529C3713ED79F5F3AA9 /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXBuildFile; fileRef = 5BD7D121AD30987C08BE10E8; };
		6A61CBB4E39BFD392D97528F /* CoreMIDI.framework */ = {isa = PBXBuildFile; fileRef = 61AE09C749B007B70A265D9B; };
		6B5560283DEEBD6DD2D6C984 /* include_juce_dsp.mm */ = {isa = PBXBuildFile; fileRef = C1E93FAF6C68A40A664422CD; };
		712D81867EC698463252FA79 /* include_juce_audio_utils.mm */ = {isa = PBXBuildFile; fileRef = EDDA01B246C6128CAF7A2914; };
		71DF4F5EB4C8305688416725 /* DemoContentComponent.cpp */ = {isa = PBXBuildFile; fileRef = E5BFC17E682AB426D203B3E6; };
		75DB074DBAE04408A0A917B7 /* Icon.icns */ = {isa = PBXBuildFile; fileRef = E0A3F113BC27B7B4D6F1D693; };
		7B4163348896EB1B86B15160 /* AVFoundation.framework */ = {isa = PBXBuildFile; fileRef = DC192EFA899E6CBE6B5CD394; };
		7F57DB52C3CEFDC26DDD38FC /* CoreImage.framework */ = {isa = PBXBuildFile; fileRef = B28EFB9D1DF0B6D6499A7DEF; };
		89AD16514B1F4133FFEA1DF9 /* WebKit.framework */ = {isa = PBXBuildFile; fileRef = 96D99A08027CA35D6A4E5CFD; };
		8C0AEA08A71075A6C765AEC9 /* AVKit.framework */ = {isa = PBXBuildFile; fileRef = 3B99CF94C44E2EE04635A439; };
		8E63755144E29269FD82C897 /* Images.xcassets */ = {isa = PBXBuildFile; fileRef = 8135645508EEFDBDCDF2ADC6; };
		9641E7E4F0B5C2A1B3E8709A /* UserNotifications.framework */ = {isa = PBXBuildFile; fileRef = 40D006CCDB1D33FF94B6ECAE; settings = { ATTRIBUTES = (Weak, ); }; };
		98D376BD41E5359370AD42D9 /* include_juce_core_CompilationTime.cpp */ = {isa = PBXBuildFile; fileRef = 640B7C54D35F5DF408327886; };
		9EACEA6BE8D0ACC72C12C080 /* include_juce_audio_devices.mm */ = {isa = PBXBuildFile; fileRef = 03A63C3CA6F24977F19C316D; };
		AC783ECD84496E0B77911EEE /* OpenGLES.framework */ = {isa = PBXBuildFile; fileRef = 34F1320BC5C23702C08DF9F0; };
		AE7FB2AC3885F4BF53A5DDA1 /* ImageIO.framework */ = {isa = PBXBuildFile; fileRef = 7983C452610C1638B7E78F12; };
		AEA090DAC5C747C50C7D3FA7 /* include_juce_osc.cpp */ = {isa = PBXBuildFile; fileRef = CE38E6469D98462A2C22C915; };
		B1981F62F6A91FD2F579A198 /* QuartzCore.framework */ = {isa = PBXBuildFile; fileRef = 23CD1A3F9067C3A0ECE7BB67; };
		B38728296BB32B7994CE28DF /* JUCEDemos.cpp */ = {isa = PBXBuildFile; fileRef = 934ACDCB3FD9D223A3481D8F; };
		BC6036F22423CA0AFF0385A7 /* include_juce_opengl.mm */ = {isa = PBXBuildFile; fileRef = 94B6C88FE30861A47CD28709; };
		BE3AD5595805C8B8845146EF /* include_juce_javascript.cpp */ = {isa = PBXBuildFile; fileRef = 4306E55904378CE5A6EB48B3; };
		C2BB2B6DA237FE0CB64C7EDA /* include_juce_analytics.cpp */ = {isa = PBXBuildFile; fileRef = 5965349393850F41DF76F350; };
		CDABEA6258EC70C65C9ACCFE /* include_juce_graphics.mm */ = {isa = PBXBuildFile; fileRef = 3E4ED41C374261CFFD309743; };
		CDEB6BA5341494AF51D07C72 /* include_juce_product_unlocking.mm */ = {isa = PBXBuildFile; fileRef = 0AB68DBAB6B7DAEDDDD5B683; };
		D183F8140174ACCDDCD230A2 /* include_juce_core.mm */ = {isa = PBXBuildFile; fileRef = 3BC9753E0CD75A36DC742EE0; };
		E2F44A968EC2598DAE33A997 /* include_juce_gui_basics.mm */ = {isa = PBXBuildFile; fileRef = A1D6D36B96B6B37C31F32829; };
		E6F58FC3ACAE774DB4D06420 /* DemoPIPs1.cpp */ = {isa = PBXBuildFile; fileRef = 3AB62BFF806112585B54DDA3; };
		ECA44A41DA8A935178C1A1F4 /* UIKit.framework */ = {isa = PBXBuildFile; fileRef = F90C8B0233A54F1445343F67; };
		EFD00925ED57B2C5EB5412FC /* Metal.framework */ = {isa = PBXBuildFile; fileRef = 3644EF58D9EB1AB436A04E77; settings = { ATTRIBUTES = (Weak, ); }; };
		F28112945CEBEA4CE8975833 /* include_juce_audio_basics.mm */ = {isa = PBXBuildFile; fileRef = 03B0F9318FD583525AB195A9; };
		F619F3887CEC064441BB6EE6 /* Main.cpp */ = {isa = PBXBuildFile; fileRef = 260481E972425474BB8155B0; };
		FF87532E62753EDFA3D29CAD /* include_juce_cryptography.mm */ = {isa = PBXBuildFile; fileRef = 6C5E26B4D28F8450435B8AE1; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		03A63C3CA6F24977F19C316D /* include_juce_audio_devices.mm */ /* include_juce_audio_devices.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_devices.mm; path = ../../JuceLibraryCode/include_juce_audio_devices.mm; sourceTree = SOURCE_ROOT; };
		03B0F9318FD583525AB195A9 /* include_juce_audio_basics.mm */ /* include_juce_audio_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_basics.mm; path = ../../JuceLibraryCode/include_juce_audio_basics.mm; sourceTree = SOURCE_ROOT; };
		061AECBF1CC7056F4155812D /* DemoPIPs2.cpp */ /* DemoPIPs2.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = DemoPIPs2.cpp; path = ../../Source/Demos/DemoPIPs2.cpp; sourceTree = SOURCE_ROOT; };
		0AB68DBAB6B7DAEDDDD5B683 /* include_juce_product_unlocking.mm */ /* include_juce_product_unlocking.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_product_unlocking.mm; path = ../../JuceLibraryCode/include_juce_product_unlocking.mm; sourceTree = SOURCE_ROOT; };
		0B36C013D9790568B481634C /* juce_audio_utils */ /* juce_audio_utils */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_utils; path = ../../../../modules/juce_audio_utils; sourceTree = SOURCE_ROOT; };
		112FFCB73597157E721BCDF2 /* juce_analytics */ /* juce_analytics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_analytics; path = ../../../../modules/juce_analytics; sourceTree = SOURCE_ROOT; };
		14CBD28B4887DAF89E27491C /* juce_cryptography */ /* juce_cryptography */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_cryptography; path = ../../../../modules/juce_cryptography; sourceTree = SOURCE_ROOT; };
		1CFE3935A3B810D5D68A2504 /* CoreMedia.framework */ /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		1FCD2145DE3FCFCF4F55A8AD /* DemoContentComponent.h */ /* DemoContentComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = DemoContentComponent.h; path = ../../Source/UI/DemoContentComponent.h; sourceTree = SOURCE_ROOT; };
		23CD1A3F9067C3A0ECE7BB67 /* QuartzCore.framework */ /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		25E5ED33876A2C752378C859 /* MainComponent.cpp */ /* MainComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = MainComponent.cpp; path = ../../Source/UI/MainComponent.cpp; sourceTree = SOURCE_ROOT; };
		260481E972425474BB8155B0 /* Main.cpp */ /* Main.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Main.cpp; path = ../../Source/Main.cpp; sourceTree = SOURCE_ROOT; };
		2992DB69DCFB7DADDE907385 /* MetalKit.framework */ /* MetalKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MetalKit.framework; path = System/Library/Frameworks/MetalKit.framework; sourceTree = SDKROOT; };
		2CDA0CB288452DA016E874BC /* App.entitlements */ /* App.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = App.entitlements; path = App.entitlements; sourceTree = SOURCE_ROOT; };
		346450C70C964FD9640B6086 /* juce_audio_processors */ /* juce_audio_processors */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_processors; path = ../../../../modules/juce_audio_processors; sourceTree = SOURCE_ROOT; };
		34F1320BC5C23702C08DF9F0 /* OpenGLES.framework */ /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		3644EF58D9EB1AB436A04E77 /* Metal.framework */ /* Metal.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Metal.framework; path = System/Library/Frameworks/Metal.framework; sourceTree = SDKROOT; };
		3AB62BFF806112585B54DDA3 /* DemoPIPs1.cpp */ /* DemoPIPs1.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = DemoPIPs1.cpp; path = ../../Source/Demos/DemoPIPs1.cpp; sourceTree = SOURCE_ROOT; };
		3B99CF94C44E2EE04635A439 /* AVKit.framework */ /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		3BC9753E0CD75A36DC742EE0 /* include_juce_core.mm */ /* include_juce_core.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_core.mm; path = ../../JuceLibraryCode/include_juce_core.mm; sourceTree = SOURCE_ROOT; };
		3E4ED41C374261CFFD309743 /* include_juce_graphics.mm */ /* include_juce_graphics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_graphics.mm; path = ../../JuceLibraryCode/include_juce_graphics.mm; sourceTree = SOURCE_ROOT; };
		40D006CCDB1D33FF94B6ECAE /* UserNotifications.framework */ /* UserNotifications.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UserNotifications.framework; path = System/Library/Frameworks/UserNotifications.framework; sourceTree = SDKROOT; };
		4306E55904378CE5A6EB48B3 /* include_juce_javascript.cpp */ /* include_juce_javascript.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_javascript.cpp; path = ../../JuceLibraryCode/include_juce_javascript.cpp; sourceTree = SOURCE_ROOT; };
		470C3E4553B513FFEF752779 /* AudioToolbox.framework */ /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		483C3A8B4FB98B54BB42CB50 /* include_juce_graphics_Harfbuzz.cpp */ /* include_juce_graphics_Harfbuzz.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_graphics_Harfbuzz.cpp; path = ../../JuceLibraryCode/include_juce_graphics_Harfbuzz.cpp; sourceTree = SOURCE_ROOT; };
		491641F7632BCC81BBA0ED85 /* juce_audio_formats */ /* juce_audio_formats */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_formats; path = ../../../../modules/juce_audio_formats; sourceTree = SOURCE_ROOT; };
		49ECA8B998B339A083674A22 /* Foundation.framework */ /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		4DF215D350FFE5E119CBA7E5 /* include_juce_box2d.cpp */ /* include_juce_box2d.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_box2d.cpp; path = ../../JuceLibraryCode/include_juce_box2d.cpp; sourceTree = SOURCE_ROOT; };
		4E9AD0EAF3CA57B548622D9A /* JuceHeader.h */ /* JuceHeader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JuceHeader.h; path = ../../JuceLibraryCode/JuceHeader.h; sourceTree = SOURCE_ROOT; };
		4EC2782DE1779A130835B64D /* Info-App.plist */ /* Info-App.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Info-App.plist"; path = "Info-App.plist"; sourceTree = SOURCE_ROOT; };
		4F0A137A4115946A346180E6 /* CoreAudio.framework */ /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		4FE6029FF76BCE9698595DC5 /* juce_product_unlocking */ /* juce_product_unlocking */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_product_unlocking; path = ../../../../modules/juce_product_unlocking; sourceTree = SOURCE_ROOT; };
		5965349393850F41DF76F350 /* include_juce_analytics.cpp */ /* include_juce_analytics.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_analytics.cpp; path = ../../JuceLibraryCode/include_juce_analytics.cpp; sourceTree = SOURCE_ROOT; };
		5A9F2000C66D24E8B01BE60B /* juce_gui_basics */ /* juce_gui_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_basics; path = ../../../../modules/juce_gui_basics; sourceTree = SOURCE_ROOT; };
		5BD7D121AD30987C08BE10E8 /* include_juce_audio_processors_lv2_libs.cpp */ /* include_juce_audio_processors_lv2_libs.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_lv2_libs.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_lv2_libs.cpp; sourceTree = SOURCE_ROOT; };
		60F2869DC345EAF2314D6C09 /* juce_audio_devices */ /* juce_audio_devices */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_devices; path = ../../../../modules/juce_audio_devices; sourceTree = SOURCE_ROOT; };
		61AE09C749B007B70A265D9B /* CoreMIDI.framework */ /* CoreMIDI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMIDI.framework; path = System/Library/Frameworks/CoreMIDI.framework; sourceTree = SDKROOT; };
		640B7C54D35F5DF408327886 /* include_juce_core_CompilationTime.cpp */ /* include_juce_core_CompilationTime.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_core_CompilationTime.cpp; path = ../../JuceLibraryCode/include_juce_core_CompilationTime.cpp; sourceTree = SOURCE_ROOT; };
		651ECE3C7BA845DDCFEE48F3 /* juce_osc */ /* juce_osc */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_osc; path = ../../../../modules/juce_osc; sourceTree = SOURCE_ROOT; };
		6847A9B2C5E3C2ED56D8D4E7 /* juce_audio_basics */ /* juce_audio_basics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_audio_basics; path = ../../../../modules/juce_audio_basics; sourceTree = SOURCE_ROOT; };
		685A261BE78585293F3EAD36 /* Assets */ /* Assets */ = {isa = PBXFileReference; lastKnownFileType = folder; name = Assets; path = ../../../Assets; sourceTree = "<group>"; };
		6C198AF93E1F6E682189E2F6 /* juce_opengl */ /* juce_opengl */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_opengl; path = ../../../../modules/juce_opengl; sourceTree = SOURCE_ROOT; };
		6C2C1AC86623F457427965EF /* juce_box2d */ /* juce_box2d */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_box2d; path = ../../../../modules/juce_box2d; sourceTree = SOURCE_ROOT; };
		6C5E26B4D28F8450435B8AE1 /* include_juce_cryptography.mm */ /* include_juce_cryptography.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_cryptography.mm; path = ../../JuceLibraryCode/include_juce_cryptography.mm; sourceTree = SOURCE_ROOT; };
		76A157A111866670A4678F04 /* CoreGraphics.framework */ /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		7983C452610C1638B7E78F12 /* ImageIO.framework */ /* ImageIO.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ImageIO.framework; path = System/Library/Frameworks/ImageIO.framework; sourceTree = SDKROOT; };
		7A5AAE9EE573FC6105CC4AAC /* SettingsContent.h */ /* SettingsContent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = SettingsContent.h; path = ../../Source/UI/SettingsContent.h; sourceTree = SOURCE_ROOT; };
		7FBD555B32197FEACDFF5AFB /* LaunchScreen.storyboard */ /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = LaunchScreen.storyboard; sourceTree = SOURCE_ROOT; };
		8135645508EEFDBDCDF2ADC6 /* Images.xcassets */ /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = DemoRunner/Images.xcassets; sourceTree = SOURCE_ROOT; };
		8447FC9882D85E6DAF0A4852 /* juce_animation */ /* juce_animation */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_animation; path = ../../../../modules/juce_animation; sourceTree = SOURCE_ROOT; };
		873F9DD54978E601102353B4 /* CoreText.framework */ /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		8CE533D611CD0984AD028D73 /* juce_graphics */ /* juce_graphics */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_graphics; path = ../../../../modules/juce_graphics; sourceTree = SOURCE_ROOT; };
		8D44097417573B38729A0179 /* include_juce_audio_processors_ara.cpp */ /* include_juce_audio_processors_ara.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_audio_processors_ara.cpp; path = ../../JuceLibraryCode/include_juce_audio_processors_ara.cpp; sourceTree = SOURCE_ROOT; };
		903CD4126C779884797EF915 /* juce_core */ /* juce_core */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_core; path = ../../../../modules/juce_core; sourceTree = SOURCE_ROOT; };
		9144821E003E15E4042B57DB /* include_juce_video.mm */ /* include_juce_video.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_video.mm; path = ../../JuceLibraryCode/include_juce_video.mm; sourceTree = SOURCE_ROOT; };
		934ACDCB3FD9D223A3481D8F /* JUCEDemos.cpp */ /* JUCEDemos.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = JUCEDemos.cpp; path = ../../Source/Demos/JUCEDemos.cpp; sourceTree = SOURCE_ROOT; };
		94B6C88FE30861A47CD28709 /* include_juce_opengl.mm */ /* include_juce_opengl.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_opengl.mm; path = ../../JuceLibraryCode/include_juce_opengl.mm; sourceTree = SOURCE_ROOT; };
		96D99A08027CA35D6A4E5CFD /* WebKit.framework */ /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = System/Library/Frameworks/WebKit.framework; sourceTree = SDKROOT; };
		979F23EA9E5E76131299E886 /* include_juce_gui_extra.mm */ /* include_juce_gui_extra.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_extra.mm; path = ../../JuceLibraryCode/include_juce_gui_extra.mm; sourceTree = SOURCE_ROOT; };
		A04E4408525F24F7DCBA000E /* Accelerate.framework */ /* Accelerate.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Accelerate.framework; path = System/Library/Frameworks/Accelerate.framework; sourceTree = SDKROOT; };
		A1D6D36B96B6B37C31F32829 /* include_juce_gui_basics.mm */ /* include_juce_gui_basics.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_gui_basics.mm; path = ../../JuceLibraryCode/include_juce_gui_basics.mm; sourceTree = SOURCE_ROOT; };
		A5256778E2EBD206B337B555 /* juce_video */ /* juce_video */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_video; path = ../../../../modules/juce_video; sourceTree = SOURCE_ROOT; };
		A6F555BE0DDF01C285BD8BF5 /* juce_dsp */ /* juce_dsp */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_dsp; path = ../../../../modules/juce_dsp; sourceTree = SOURCE_ROOT; };
		A9315F8368A5771EC39631CB /* juce_gui_extra */ /* juce_gui_extra */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_gui_extra; path = ../../../../modules/juce_gui_extra; sourceTree = SOURCE_ROOT; };
		AAF88452B7774FB605990B31 /* UniformTypeIdentifiers.framework */ /* UniformTypeIdentifiers.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UniformTypeIdentifiers.framework; path = System/Library/Frameworks/UniformTypeIdentifiers.framework; sourceTree = SDKROOT; };
		B28EFB9D1DF0B6D6499A7DEF /* CoreImage.framework */ /* CoreImage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreImage.framework; path = System/Library/Frameworks/CoreImage.framework; sourceTree = SDKROOT; };
		B2BC383CE102EECCF49C7AF7 /* IntroScreen.h */ /* IntroScreen.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = IntroScreen.h; path = ../../Source/Demos/IntroScreen.h; sourceTree = SOURCE_ROOT; };
		B4389672DA4CC8E0A531062D /* CoreAudioKit.framework */ /* CoreAudioKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudioKit.framework; path = System/Library/Frameworks/CoreAudioKit.framework; sourceTree = SDKROOT; };
		C1E93FAF6C68A40A664422CD /* include_juce_dsp.mm */ /* include_juce_dsp.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_dsp.mm; path = ../../JuceLibraryCode/include_juce_dsp.mm; sourceTree = SOURCE_ROOT; };
		C64CA4082EC267CDD63E6623 /* juce_javascript */ /* juce_javascript */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_javascript; path = ../../../../modules/juce_javascript; sourceTree = SOURCE_ROOT; };
		CB4C64BACC1CA4AAD6D69565 /* include_juce_animation.cpp */ /* include_juce_animation.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_animation.cpp; path = ../../JuceLibraryCode/include_juce_animation.cpp; sourceTree = SOURCE_ROOT; };
		CCC8AEBCE55002F970222BAD /* include_juce_graphics_Sheenbidi.c */ /* include_juce_graphics_Sheenbidi.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = include_juce_graphics_Sheenbidi.c; path = ../../JuceLibraryCode/include_juce_graphics_Sheenbidi.c; sourceTree = SOURCE_ROOT; };
		CE38E6469D98462A2C22C915 /* include_juce_osc.cpp */ /* include_juce_osc.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = include_juce_osc.cpp; path = ../../JuceLibraryCode/include_juce_osc.cpp; sourceTree = SOURCE_ROOT; };
		CFF2BBEB242CC8B3B904B5F9 /* App */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = DemoRunner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		DC192EFA899E6CBE6B5CD394 /* AVFoundation.framework */ /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		E061A1C75FA5722167FC4997 /* include_juce_audio_formats.mm */ /* include_juce_audio_formats.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_formats.mm; path = ../../JuceLibraryCode/include_juce_audio_formats.mm; sourceTree = SOURCE_ROOT; };
		E07FC48041C3E9F9721F3BCE /* CoreServices.framework */ /* CoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreServices.framework; path = System/Library/Frameworks/CoreServices.framework; sourceTree = SDKROOT; };
		E0A3F113BC27B7B4D6F1D693 /* Icon.icns */ /* Icon.icns */ = {isa = PBXFileReference; lastKnownFileType = file.icns; name = Icon.icns; path = Icon.icns; sourceTree = SOURCE_ROOT; };
		E5BFC17E682AB426D203B3E6 /* DemoContentComponent.cpp */ /* DemoContentComponent.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = DemoContentComponent.cpp; path = ../../Source/UI/DemoContentComponent.cpp; sourceTree = SOURCE_ROOT; };
		E67AB94002886AF67437D6AE /* include_juce_audio_processors.mm */ /* include_juce_audio_processors.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_processors.mm; path = ../../JuceLibraryCode/include_juce_audio_processors.mm; sourceTree = SOURCE_ROOT; };
		EB68BD1224CD9748BFA332C0 /* MainComponent.h */ /* MainComponent.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = MainComponent.h; path = ../../Source/UI/MainComponent.h; sourceTree = SOURCE_ROOT; };
		ECE79F1433E92BB6213C86F5 /* JUCEAppIcon.png */ /* JUCEAppIcon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = JUCEAppIcon.png; path = ../../Source/JUCEAppIcon.png; sourceTree = SOURCE_ROOT; };
		EDDA01B246C6128CAF7A2914 /* include_juce_audio_utils.mm */ /* include_juce_audio_utils.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_audio_utils.mm; path = ../../JuceLibraryCode/include_juce_audio_utils.mm; sourceTree = SOURCE_ROOT; };
		EE6BDC78B539D27E65E92265 /* JUCEDemos.h */ /* JUCEDemos.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = JUCEDemos.h; path = ../../Source/Demos/JUCEDemos.h; sourceTree = SOURCE_ROOT; };
		F5F2EA2238973488632FC322 /* include_juce_data_structures.mm */ /* include_juce_data_structures.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_data_structures.mm; path = ../../JuceLibraryCode/include_juce_data_structures.mm; sourceTree = SOURCE_ROOT; };
		F90C8B0233A54F1445343F67 /* UIKit.framework */ /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		FB1FC613CE260140F1CFD21B /* juce_events */ /* juce_events */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_events; path = ../../../../modules/juce_events; sourceTree = SOURCE_ROOT; };
		FCD0D758C5767944BEC29730 /* include_juce_events.mm */ /* include_juce_events.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; name = include_juce_events.mm; path = ../../JuceLibraryCode/include_juce_events.mm; sourceTree = SOURCE_ROOT; };
		FDC3CA8D1403C169659F7D8C /* juce_data_structures */ /* juce_data_structures */ = {isa = PBXFileReference; lastKnownFileType = folder; name = juce_data_structures; path = ../../../../modules/juce_data_structures; sourceTree = SOURCE_ROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		4B1F6E32C9FC8D779B21C1AF = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				48CF0B02E1D06E5DA51E6270,
				163B0CF2DD0990A63DF1D5A6,
				7B4163348896EB1B86B15160,
				8C0AEA08A71075A6C765AEC9,
				1351A13E78F38741C6075600,
				527DA2E6827BAFDDD3E8E80F,
				34A4931AF1DD424D3A400EEF,
				7F57DB52C3CEFDC26DDD38FC,
				46071CE2B98B562B7BF27CB1,
				6A61CBB4E39BFD392D97528F,
				6658EEC5F9D63D3419EB7098,
				0B0CE6D5062E5C02A41F24BC,
				5E4310B3F6BB639875D3E9B8,
				AE7FB2AC3885F4BF53A5DDA1,
				AC783ECD84496E0B77911EEE,
				B1981F62F6A91FD2F579A198,
				ECA44A41DA8A935178C1A1F4,
				89AD16514B1F4133FFEA1DF9,
				EFD00925ED57B2C5EB5412FC,
				10D769051F1431A67AD2CB40,
				41AE56F8671DFE80720A6A18,
				9641E7E4F0B5C2A1B3E8709A,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4452EAA652B65A9AE648288C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A04E4408525F24F7DCBA000E,
				470C3E4553B513FFEF752779,
				DC192EFA899E6CBE6B5CD394,
				3B99CF94C44E2EE04635A439,
				4F0A137A4115946A346180E6,
				B4389672DA4CC8E0A531062D,
				76A157A111866670A4678F04,
				B28EFB9D1DF0B6D6499A7DEF,
				1CFE3935A3B810D5D68A2504,
				61AE09C749B007B70A265D9B,
				E07FC48041C3E9F9721F3BCE,
				873F9DD54978E601102353B4,
				49ECA8B998B339A083674A22,
				7983C452610C1638B7E78F12,
				34F1320BC5C23702C08DF9F0,
				23CD1A3F9067C3A0ECE7BB67,
				F90C8B0233A54F1445343F67,
				96D99A08027CA35D6A4E5CFD,
				3644EF58D9EB1AB436A04E77,
				2992DB69DCFB7DADDE907385,
				AAF88452B7774FB605990B31,
				40D006CCDB1D33FF94B6ECAE,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		5A0B2CEF393A25C6D4B1B76C /* JUCE Modules */ = {
			isa = PBXGroup;
			children = (
				112FFCB73597157E721BCDF2,
				8447FC9882D85E6DAF0A4852,
				6847A9B2C5E3C2ED56D8D4E7,
				60F2869DC345EAF2314D6C09,
				491641F7632BCC81BBA0ED85,
				346450C70C964FD9640B6086,
				0B36C013D9790568B481634C,
				6C2C1AC86623F457427965EF,
				903CD4126C779884797EF915,
				14CBD28B4887DAF89E27491C,
				FDC3CA8D1403C169659F7D8C,
				A6F555BE0DDF01C285BD8BF5,
				FB1FC613CE260140F1CFD21B,
				8CE533D611CD0984AD028D73,
				5A9F2000C66D24E8B01BE60B,
				A9315F8368A5771EC39631CB,
				C64CA4082EC267CDD63E6623,
				6C198AF93E1F6E682189E2F6,
				651ECE3C7BA845DDCFEE48F3,
				4FE6029FF76BCE9698595DC5,
				A5256778E2EBD206B337B555,
			);
			name = "JUCE Modules";
			sourceTree = "<group>";
		};
		61F3057D838D7DABB0FA3D34 /* JUCE Library Code */ = {
			isa = PBXGroup;
			children = (
				5965349393850F41DF76F350,
				CB4C64BACC1CA4AAD6D69565,
				03B0F9318FD583525AB195A9,
				03A63C3CA6F24977F19C316D,
				E061A1C75FA5722167FC4997,
				E67AB94002886AF67437D6AE,
				8D44097417573B38729A0179,
				5BD7D121AD30987C08BE10E8,
				EDDA01B246C6128CAF7A2914,
				4DF215D350FFE5E119CBA7E5,
				3BC9753E0CD75A36DC742EE0,
				640B7C54D35F5DF408327886,
				6C5E26B4D28F8450435B8AE1,
				F5F2EA2238973488632FC322,
				C1E93FAF6C68A40A664422CD,
				FCD0D758C5767944BEC29730,
				3E4ED41C374261CFFD309743,
				483C3A8B4FB98B54BB42CB50,
				CCC8AEBCE55002F970222BAD,
				A1D6D36B96B6B37C31F32829,
				979F23EA9E5E76131299E886,
				4306E55904378CE5A6EB48B3,
				94B6C88FE30861A47CD28709,
				CE38E6469D98462A2C22C915,
				0AB68DBAB6B7DAEDDDD5B683,
				9144821E003E15E4042B57DB,
				4E9AD0EAF3CA57B548622D9A,
			);
			name = "JUCE Library Code";
			sourceTree = "<group>";
		};
		6F933968486D9AD9FE112622 /* Source */ = {
			isa = PBXGroup;
			children = (
				CD575607FAA297480EE95F13,
				BB12537403CC24C02622582C,
				260481E972425474BB8155B0,
				ECE79F1433E92BB6213C86F5,
			);
			name = Source;
			sourceTree = "<group>";
		};
		91A9A0FE9DF4F4E10009EEC7 /* Source */ = {
			isa = PBXGroup;
			children = (
				9683F931FA1B8B85FA8C4BD8,
				5A0B2CEF393A25C6D4B1B76C,
				61F3057D838D7DABB0FA3D34,
				D87DCD5DA4EC8D78DFF37FCC,
				4452EAA652B65A9AE648288C,
				BFDAF16175D03695EEB466BC,
			);
			name = Source;
			sourceTree = "<group>";
		};
		9683F931FA1B8B85FA8C4BD8 /* DemoRunner */ = {
			isa = PBXGroup;
			children = (
				6F933968486D9AD9FE112622,
			);
			name = DemoRunner;
			sourceTree = "<group>";
		};
		BB12537403CC24C02622582C /* UI */ = {
			isa = PBXGroup;
			children = (
				E5BFC17E682AB426D203B3E6,
				1FCD2145DE3FCFCF4F55A8AD,
				25E5ED33876A2C752378C859,
				EB68BD1224CD9748BFA332C0,
				7A5AAE9EE573FC6105CC4AAC,
			);
			name = UI;
			sourceTree = "<group>";
		};
		BFDAF16175D03695EEB466BC /* Products */ = {
			isa = PBXGroup;
			children = (
				CFF2BBEB242CC8B3B904B5F9,
			);
			name = Products;
			sourceTree = "<group>";
		};
		CD575607FAA297480EE95F13 /* Demos */ = {
			isa = PBXGroup;
			children = (
				3AB62BFF806112585B54DDA3,
				061AECBF1CC7056F4155812D,
				B2BC383CE102EECCF49C7AF7,
				934ACDCB3FD9D223A3481D8F,
				EE6BDC78B539D27E65E92265,
			);
			name = Demos;
			sourceTree = "<group>";
		};
		D87DCD5DA4EC8D78DFF37FCC /* Resources */ = {
			isa = PBXGroup;
			children = (
				685A261BE78585293F3EAD36,
				4EC2782DE1779A130835B64D,
				8135645508EEFDBDCDF2ADC6,
				7FBD555B32197FEACDFF5AFB,
				E0A3F113BC27B7B4D6F1D693,
			);
			name = Resources;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		291E01DCBE746A376DBFA4D1 /* DemoRunner - App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 413FBEAEC84DFEC41133C78B;
			buildPhases = (
				57134FDD813875865F5B2057,
				ED916866997CA4F40C7C1016,
				4B1F6E32C9FC8D779B21C1AF,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "DemoRunner - App";
			productName = DemoRunner;
			productReference = CFF2BBEB242CC8B3B904B5F9;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		AC6F0E9A0809A184B2C2B7DE = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1340;
				ORGANIZATIONNAME = "Raw Material Software Limited";
				TargetAttributes = {
					291E01DCBE746A376DBFA4D1 = {
						SystemCapabilities = {
							com.apple.ApplicationGroups.iOS = {
								enabled = 0;
							};
							com.apple.com.apple.iCloud = {
								enabled = 1;
							};
							com.apple.HardenedRuntime = {
								enabled = 0;
							};
							com.apple.InAppPurchase = {
								enabled = 0;
							};
							com.apple.InterAppAudio = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 0;
							};
							com.apple.Sandbox = {
								enabled = 0;
							};
						};
					};
				};
			};
			buildConfigurationList = 80E8AD1971F52B06F4D28891;
			compatibilityVersion = "Xcode 3.2";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 91A9A0FE9DF4F4E10009EEC7;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				291E01DCBE746A376DBFA4D1,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		57134FDD813875865F5B2057 = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				47ED2C78B05B8A6A00E36C46,
				8E63755144E29269FD82C897,
				1C9AA8660D0DE180064ACF7B,
				75DB074DBAE04408A0A917B7,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		ED916866997CA4F40C7C1016 = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E6F58FC3ACAE774DB4D06420,
				36E115D98311F12AA06710E6,
				B38728296BB32B7994CE28DF,
				71DF4F5EB4C8305688416725,
				4FBBB55F4E347757F74F1F41,
				F619F3887CEC064441BB6EE6,
				C2BB2B6DA237FE0CB64C7EDA,
				55F120E70543228568573D4A,
				F28112945CEBEA4CE8975833,
				9EACEA6BE8D0ACC72C12C080,
				26652AB1BB77C8A39434775F,
				2707968B431D83AC7E28E49B,
				5EB6872A39122A5AB67E544E,
				67D7E529C3713ED79F5F3AA9,
				712D81867EC698463252FA79,
				611298FAC1A543BDD10D4C41,
				D183F8140174ACCDDCD230A2,
				98D376BD41E5359370AD42D9,
				FF87532E62753EDFA3D29CAD,
				2F76CA28C8C0EFC7453D0EB8,
				6B5560283DEEBD6DD2D6C984,
				1DBDFEDE359CFC84F8E3DE4C,
				CDABEA6258EC70C65C9ACCFE,
				11D7AB57EC28DB48A066F7AD,
				1BA36E8CA4B9E8D3F3B57D24,
				E2F44A968EC2598DAE33A997,
				5CB78489F16E82144914972D,
				BE3AD5595805C8B8845146EF,
				BC6036F22423CA0AFF0385A7,
				AEA090DAC5C747C50C7D3FA7,
				CDEB6BA5341494AF51D07C72,
				0140787C0118A95E37DE90B4,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		07EA85D22270E8EA13CA0BBE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_NAME = "DemoRunner";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Release;
		};
		69330F27DD2C71609336C7D2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				CODE_SIGN_ENTITLEMENTS = "App.entitlements";
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				DEAD_CODE_STRIPPING = YES;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_OPTIMIZATION_LEVEL = 3;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_NDEBUG=1",
					"NDEBUG=1",
					"JUCE_CONTENT_SHARING=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_box2d=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_cryptography=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_MODULE_AVAILABLE_juce_product_unlocking=1",
					"JUCE_MODULE_AVAILABLE_juce_video=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_USE_MP3AUDIOFORMAT=1",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_LV2=1",
					"JUCE_ALLOW_STATIC_NULL_VARIABLES=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_USE_CAMERA=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCE_DEMO_RUNNER=1",
					"JUCE_UNIT_TESTS=1",
					"JUCE_PUSH_NOTIFICATIONS=1",
					"JUCE_SILENCE_XCODE_15_LINKER_WARNING=1",
					"JUCER_XCODE_IPHONE_5BC26AE3=1",
					"JUCE_APP_VERSION=8.0.7",
					"JUCE_APP_VERSION_HEX=0x80007",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-App.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Applications";
				LLVM_LTO = YES;
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2 $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				OTHER_CFLAGS = "-Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_CPLUSPLUSFLAGS = "-Woverloaded-virtual -Wreorder -Wzero-as-null-pointer-constant -Wunused-private-field -Winconsistent-missing-destructor-override -Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_LDFLAGS = "-Wl,-weak_reference_mismatches,weak";
				PRODUCT_BUNDLE_IDENTIFIER = com.rmsl.jucedemorunner;
				PRODUCT_NAME = "DemoRunner";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGLES;
			};
			name = Release;
		};
		B18D059E5616FA729F764229 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_LINK_OBJC_RUNTIME = NO;
				CODE_SIGN_ENTITLEMENTS = "App.entitlements";
				COMBINE_HIDPI_IMAGES = YES;
				CONFIGURATION_BUILD_DIR = "$(PROJECT_DIR)/build/$(CONFIGURATION)";
				COPY_PHASE_STRIP = NO;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"_DEBUG=1",
					"DEBUG=1",
					"JUCE_CONTENT_SHARING=1",
					"JUCE_PROJUCER_VERSION=0x80007",
					"JUCE_MODULE_AVAILABLE_juce_analytics=1",
					"JUCE_MODULE_AVAILABLE_juce_animation=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_devices=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_formats=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_processors=1",
					"JUCE_MODULE_AVAILABLE_juce_audio_utils=1",
					"JUCE_MODULE_AVAILABLE_juce_box2d=1",
					"JUCE_MODULE_AVAILABLE_juce_core=1",
					"JUCE_MODULE_AVAILABLE_juce_cryptography=1",
					"JUCE_MODULE_AVAILABLE_juce_data_structures=1",
					"JUCE_MODULE_AVAILABLE_juce_dsp=1",
					"JUCE_MODULE_AVAILABLE_juce_events=1",
					"JUCE_MODULE_AVAILABLE_juce_graphics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_basics=1",
					"JUCE_MODULE_AVAILABLE_juce_gui_extra=1",
					"JUCE_MODULE_AVAILABLE_juce_javascript=1",
					"JUCE_MODULE_AVAILABLE_juce_opengl=1",
					"JUCE_MODULE_AVAILABLE_juce_osc=1",
					"JUCE_MODULE_AVAILABLE_juce_product_unlocking=1",
					"JUCE_MODULE_AVAILABLE_juce_video=1",
					"JUCE_GLOBAL_MODULE_SETTINGS_INCLUDED=1",
					"JUCE_USE_MP3AUDIOFORMAT=1",
					"JUCE_PLUGINHOST_VST3=1",
					"JUCE_PLUGINHOST_LV2=1",
					"JUCE_ALLOW_STATIC_NULL_VARIABLES=0",
					"JUCE_STRICT_REFCOUNTEDPOINTER=1",
					"JUCE_USE_CAMERA=1",
					"JUCE_STANDALONE_APPLICATION=1",
					"JUCE_DEMO_RUNNER=1",
					"JUCE_UNIT_TESTS=1",
					"JUCE_PUSH_NOTIFICATIONS=1",
					"JUCE_SILENCE_XCODE_15_LINKER_WARNING=1",
					"JUCER_XCODE_IPHONE_5BC26AE3=1",
					"JUCE_APP_VERSION=8.0.7",
					"JUCE_APP_VERSION_HEX=0x80007",
					"JucePlugin_Build_VST=0",
					"JucePlugin_Build_VST3=0",
					"JucePlugin_Build_AU=0",
					"JucePlugin_Build_AUv3=0",
					"JucePlugin_Build_AAX=0",
					"JucePlugin_Build_Standalone=0",
					"JucePlugin_Build_Unity=0",
					"JucePlugin_Build_LV2=0",
				);
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK",
					"$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK",
					"$(SRCROOT)/../../JuceLibraryCode",
					"$(SRCROOT)/../../../../modules",
					"$(inherited)",
				);
				INFOPLIST_FILE = Info-App.plist;
				INFOPLIST_PREPROCESS = NO;
				INSTALL_PATH = "$(HOME)/Applications";
				MTL_HEADER_SEARCH_PATHS = "$(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lilv $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sratom $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord/src $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/sord $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/serd $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK/lv2 $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/LV2_SDK $(SRCROOT)/../../../../modules/juce_audio_processors/format_types/VST3_SDK $(SRCROOT)/../../JuceLibraryCode $(SRCROOT)/../../../../modules";
				OTHER_CFLAGS = "-Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_CPLUSPLUSFLAGS = "-Woverloaded-virtual -Wreorder -Wzero-as-null-pointer-constant -Wunused-private-field -Winconsistent-missing-destructor-override -Wall -Wcast-align -Wfloat-equal -Wno-ignored-qualifiers -Wsign-compare -Wsign-conversion -Wstrict-aliasing -Wswitch-enum -Wuninitialized -Wunreachable-code -Wunused-parameter -Wmissing-field-initializers -Wshadow-all -Wshorten-64-to-32 -Wconversion -Wint-conversion -Wconditional-uninitialized -Wconstant-conversion -Wbool-conversion -Wextra-semi -Wshift-sign-overflow -Wmissing-prototypes -Wnullable-to-nonnull-conversion -Wpedantic -Wdeprecated -Wunguarded-availability -Wunguarded-availability-new";
				OTHER_LDFLAGS = "-Wl,-weak_reference_mismatches,weak";
				PRODUCT_BUNDLE_IDENTIFIER = com.rmsl.jucedemorunner;
				PRODUCT_NAME = "DemoRunner";
				USE_HEADERMAP = NO;
				VALIDATE_WORKSPACE_SKIPPED_SDK_FRAMEWORKS = OpenGLES;
			};
			name = Debug;
		};
		C01EC82F42B640CA1E54AD53 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_INLINES_ARE_PRIVATE_EXTERN = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_TYPECHECK_CALLS_TO_PRINTF = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "DemoRunner";
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				WARNING_CFLAGS = "-Wreorder";
				ZERO_LINK = NO;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		413FBEAEC84DFEC41133C78B = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B18D059E5616FA729F764229,
				69330F27DD2C71609336C7D2,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		80E8AD1971F52B06F4D28891 = {
			isa = XCConfigurationList;
			buildConfigurations = (
				07EA85D22270E8EA13CA0BBE,
				C01EC82F42B640CA1E54AD53,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = AC6F0E9A0809A184B2C2B7DE /* Project object */;
}
