#include "PluginProcessor.h"
#include "PluginEditor.h"

//==============================================================================
// CompliLookAndFeel Implementation
//==============================================================================
CompliLookAndFeel::CompliLookAndFeel()
{
    // Set custom colors based on the HTML mockup
    setColour(juce::ResizableWindow::backgroundColourId, juce::Colour(0xff0a0a1a));
    setColour(juce::Slider::thumbColourId, juce::Colour(0xff64ffda));
    setColour(juce::Slider::trackColourId, juce::Colour(0x40ffffff));
    setColour(juce::Slider::backgroundColourId, juce::Colour(0x20ffffff));
    setColour(juce::ComboBox::backgroundColourId, juce::Colour(0x26ffffff));
    setColour(juce::ComboBox::outlineColourId, juce::Colour(0xff64ffda));
    setColour(juce::TextButton::buttonColourId, juce::Colour(0x1affffff));
    setColour(juce::TextButton::buttonOnColourId, juce::Colour(0xff64ffda));
}

void CompliLookAndFeel::drawRotarySlider(juce::Graphics& g, int x, int y, int width, int height,
                                        float sliderPos, float rotaryStartAngle, float rotaryEndAngle,
                                        juce::Slider&)
{
    auto radius = (float) juce::jmin(width / 2, height / 2) - 4.0f;
    auto centreX = (float) x + (float) width * 0.5f;
    auto centreY = (float) y + (float) height * 0.5f;
    auto rx = centreX - radius;
    auto ry = centreY - radius;
    auto rw = radius * 2.0f;
    auto angle = rotaryStartAngle + sliderPos * (rotaryEndAngle - rotaryStartAngle);

    // Fill
    g.setColour(juce::Colour(0x1affffff));
    g.fillEllipse(rx, ry, rw, rw);

    // Outline
    g.setColour(juce::Colour(0x33ffffff));
    g.drawEllipse(rx, ry, rw, rw, 2.0f);

    // Pointer
    juce::Path p;
    auto pointerLength = radius * 0.33f;
    auto pointerThickness = 2.0f;
    p.addRectangle(-pointerThickness * 0.5f, -radius, pointerThickness, pointerLength);
    p.applyTransform(juce::AffineTransform::rotation(angle).translated(centreX, centreY));

    // Gradient for pointer
    juce::ColourGradient gradient(juce::Colour(0xff64ffda), 0, 0, juce::Colour(0xff4ade80), 0, pointerLength, false);
    g.setGradientFill(gradient);
    g.fillPath(p);
}

void CompliLookAndFeel::drawToggleButton(juce::Graphics& g, juce::ToggleButton& button,
                                        bool, bool)
{
    auto bounds = button.getLocalBounds().toFloat();
    auto cornerSize = bounds.getHeight() * 0.5f;

    // Background
    g.setColour(button.getToggleState() ? juce::Colour(0x4064ffda) : juce::Colour(0x1affffff));
    g.fillRoundedRectangle(bounds, cornerSize);

    // Border
    g.setColour(button.getToggleState() ? juce::Colour(0xff64ffda) : juce::Colour(0x33ffffff));
    g.drawRoundedRectangle(bounds, cornerSize, 2.0f);

    // Thumb
    auto thumbBounds = bounds.reduced(2.0f);
    auto thumbSize = thumbBounds.getHeight();
    auto thumbX = button.getToggleState() ?
        thumbBounds.getRight() - thumbSize : thumbBounds.getX();

    juce::Rectangle<float> thumbRect(thumbX, thumbBounds.getY(), thumbSize, thumbSize);

    if (button.getToggleState())
    {
        juce::ColourGradient thumbGradient(juce::Colour(0xff64ffda), 0, 0, juce::Colour(0xff4ade80), 0, thumbSize, false);
        g.setGradientFill(thumbGradient);
    }
    else
    {
        g.setColour(juce::Colours::white);
    }

    g.fillEllipse(thumbRect);
}

//==============================================================================
// LevelMeter Implementation
//==============================================================================
LevelMeter::LevelMeter()
{
    startTimerHz(30); // 30 FPS update rate
}

void LevelMeter::paint(juce::Graphics& g)
{
    auto bounds = getLocalBounds().toFloat();

    // Background
    g.setColour(juce::Colour(0x80000000));
    g.fillRoundedRectangle(bounds, 4.0f);

    // Border
    g.setColour(juce::Colour(0x26ffffff));
    g.drawRoundedRectangle(bounds, 4.0f, 1.0f);

    // Level fill
    if (displayLevel > 0.0f)
    {
        auto fillHeight = bounds.getHeight() * displayLevel;
        auto fillBounds = bounds.withTop(bounds.getBottom() - fillHeight);

        juce::ColourGradient gradient;
        if (displayLevel < 0.7f)
        {
            gradient = juce::ColourGradient(juce::Colour(0xff4ade80), 0.0f, fillBounds.getBottom(),
                                          juce::Colour(0xff22c55e), 0.0f, fillBounds.getY(), false);
        }
        else if (displayLevel < 0.9f)
        {
            gradient = juce::ColourGradient(juce::Colour(0xff3b82f6), 0.0f, fillBounds.getBottom(),
                                          juce::Colour(0xff1d4ed8), 0.0f, fillBounds.getY(), false);
        }
        else
        {
            gradient = juce::ColourGradient(juce::Colour(0xfff59e0b), 0.0f, fillBounds.getBottom(),
                                          juce::Colour(0xffd97706), 0.0f, fillBounds.getY(), false);
        }

        g.setGradientFill(gradient);
        g.fillRoundedRectangle(fillBounds, 3.0f);
    }
}

void LevelMeter::setLevel(float newLevel)
{
    level = juce::jlimit(0.0f, 1.0f, newLevel);
}

void LevelMeter::timerCallback()
{
    // Smooth level animation
    displayLevel += (level - displayLevel) * 0.2f;
    if (std::abs(displayLevel - level) < 0.001f)
        displayLevel = level;

    repaint();
}

//==============================================================================
// CompliAudioProcessorEditor Implementation
//==============================================================================
CompliAudioProcessorEditor::CompliAudioProcessorEditor (CompliAudioProcessor& p)
    : AudioProcessorEditor (&p), processorRef (p)
{
    // Set the look and feel
    setLookAndFeel(&compliLookAndFeel);

    // Set up preset selector
    presetSelector.addItem("✓ Voice Optimized (Default)", 1);
    presetSelector.addItem("Broadcast", 2);
    presetSelector.addItem("Podcast", 3);
    presetSelector.addItem("Gaming", 4);
    presetSelector.addItem("Custom", 5);
    presetSelector.setSelectedId(1);
    addAndMakeVisible(presetSelector);

    // Set up input selector
    inputSelector.addItem("Built-in Microphone", 1);
    inputSelector.addItem("USB Microphone", 2);
    inputSelector.addItem("Audio Interface", 3);
    inputSelector.addItem("Headset Microphone", 4);
    inputSelector.setSelectedId(1);
    addAndMakeVisible(inputSelector);

    // Set up compressor sliders
    setupSlider(thresholdSlider, "threshold", "dB");
    setupSlider(ratioSlider, "ratio", ":1");
    setupSlider(attackSlider, "attack", "ms");
    setupSlider(releaseSlider, "release", "ms");
    setupSlider(makeupGainSlider, "makeupGain", "dB");

    // Set up limiter controls
    setupSlider(limiterThresholdSlider, "limiterThreshold", "dB");
    limiterToggle.setToggleState(true, juce::dontSendNotification);
    addAndMakeVisible(limiterToggle);

    // Set up bypass button
    bypassButton.setButtonText("BYPASS");
    bypassButton.setColour(juce::TextButton::buttonColourId, juce::Colour(0x1affffff));
    bypassButton.setColour(juce::TextButton::textColourOffId, juce::Colour(0xe6ffffff));
    addAndMakeVisible(bypassButton);

    // Set up labels
    setupLabel(thresholdLabel, "THRESHOLD");
    setupLabel(ratioLabel, "RATIO");
    setupLabel(attackLabel, "ATTACK");
    setupLabel(releaseLabel, "RELEASE");
    setupLabel(makeupGainLabel, "MAKEUP");
    setupLabel(limiterThresholdLabel, "THRESHOLD");

    // Set up level meters
    addAndMakeVisible(inputMeter);
    addAndMakeVisible(outputMeter);
    addAndMakeVisible(grMeter);

    setupLabel(inputMeterLabel, "INPUT");
    setupLabel(outputMeterLabel, "OUTPUT");
    setupLabel(grMeterLabel, "GR");

    setupLabel(inputValueLabel, "-8 dB");
    setupLabel(outputValueLabel, "-3 dB");
    setupLabel(grValueLabel, "-4 dB");

    // Create parameter attachments
    thresholdAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
        processorRef.apvts, "threshold", thresholdSlider);
    ratioAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
        processorRef.apvts, "ratio", ratioSlider);
    attackAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
        processorRef.apvts, "attack", attackSlider);
    releaseAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
        processorRef.apvts, "release", releaseSlider);
    makeupGainAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
        processorRef.apvts, "makeupGain", makeupGainSlider);
    limiterThresholdAttachment = std::make_unique<juce::AudioProcessorValueTreeState::SliderAttachment>(
        processorRef.apvts, "limiterThreshold", limiterThresholdSlider);
    limiterToggleAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ButtonAttachment>(
        processorRef.apvts, "limiterEnabled", limiterToggle);
    presetAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ComboBoxAttachment>(
        processorRef.apvts, "preset", presetSelector);
    bypassAttachment = std::make_unique<juce::AudioProcessorValueTreeState::ButtonAttachment>(
        processorRef.apvts, "bypass", bypassButton);

    // Start timer for level meter updates
    startTimerHz(30);

    // Set the editor size to match the HTML mockup proportions
    setSize (1200, 800);
}

CompliAudioProcessorEditor::~CompliAudioProcessorEditor()
{
    setLookAndFeel(nullptr);
}

void CompliAudioProcessorEditor::setupSlider(juce::Slider& slider, const juce::String& parameterID, const juce::String& suffix)
{
    slider.setSliderStyle(juce::Slider::RotaryHorizontalVerticalDrag);
    slider.setTextBoxStyle(juce::Slider::NoTextBox, false, 0, 0);
    slider.setLookAndFeel(&compliLookAndFeel);
    addAndMakeVisible(slider);
}

void CompliAudioProcessorEditor::setupLabel(juce::Label& label, const juce::String& text)
{
    label.setText(text, juce::dontSendNotification);
    label.setFont(juce::FontOptions(11.0f, juce::Font::bold));
    label.setColour(juce::Label::textColourId, juce::Colour(0xe6ffffff));
    label.setJustificationType(juce::Justification::centred);
    addAndMakeVisible(label);
}

void CompliAudioProcessorEditor::timerCallback()
{
    // Update level meters with values from processor
    float inputLevelDb = processorRef.inputLevel.load();
    float outputLevelDb = processorRef.outputLevel.load();
    float grLevelDb = processorRef.gainReduction.load();

    // Convert dB to 0-1 range for meters (assuming -60dB to 0dB range)
    float inputLevel = juce::jlimit(0.0f, 1.0f, (inputLevelDb + 60.0f) / 60.0f);
    float outputLevel = juce::jlimit(0.0f, 1.0f, (outputLevelDb + 60.0f) / 60.0f);
    float grLevel = juce::jlimit(0.0f, 1.0f, grLevelDb / 20.0f); // 0-20dB GR range

    inputMeter.setLevel(inputLevel);
    outputMeter.setLevel(outputLevel);
    grMeter.setLevel(grLevel);

    // Update value labels
    inputValueLabel.setText(juce::String(inputLevelDb, 1) + " dB", juce::dontSendNotification);
    outputValueLabel.setText(juce::String(outputLevelDb, 1) + " dB", juce::dontSendNotification);
    grValueLabel.setText(juce::String(grLevelDb, 1) + " dB", juce::dontSendNotification);
}

void CompliAudioProcessorEditor::paint(juce::Graphics& g)
{
    // Background gradient matching the HTML mockup
    juce::ColourGradient backgroundGradient(
        juce::Colour(0xff0a0a1a), 0, 0,
        juce::Colour(0xff1a1a2e), getWidth() * 0.3f, 0, false);
    backgroundGradient.addColour(0.7, juce::Colour(0xff16213e));
    backgroundGradient.addColour(1.0, juce::Colour(0xff0f3460));

    g.setGradientFill(backgroundGradient);
    g.fillAll();

    // Main container with backdrop blur effect simulation
    auto mainBounds = getLocalBounds().reduced(20);
    g.setColour(juce::Colour(0x590f0f23)); // Semi-transparent background
    g.fillRoundedRectangle(mainBounds.toFloat(), 0);

    // Header
    auto headerBounds = mainBounds.removeFromTop(80);
    g.setFont(juce::FontOptions(32.0f, juce::Font::bold));

    // "compli" title with gradient
    juce::ColourGradient titleGradient(
        juce::Colour(0xff64ffda), 0, 0,
        juce::Colour(0xff00bcd4), headerBounds.getWidth() * 0.5f, 0, false);
    titleGradient.addColour(1.0, juce::Colour(0xff2196f3));

    g.setGradientFill(titleGradient);
    g.drawText("compli", headerBounds.removeFromTop(40), juce::Justification::centred);

    // Subtitle
    g.setColour(juce::Colour(0x99ffffff));
    g.setFont(juce::FontOptions(14.0f, juce::Font::bold));
    g.drawText("AUDIO PROCESSOR", headerBounds, juce::Justification::centred);

    // Section backgrounds
    auto contentBounds = mainBounds.reduced(16);

    // Input section background
    auto inputBounds = juce::Rectangle<int>(0, 0, 280, contentBounds.getHeight() - 60).withPosition(contentBounds.getTopLeft());
    drawSectionBackground(g, inputBounds, "INPUT");

    // Compressor section background
    auto compressorBounds = juce::Rectangle<int>(300, 0, contentBounds.getWidth() - 580, contentBounds.getHeight() - 60).withPosition(contentBounds.getTopLeft().translated(300, 0));
    drawSectionBackground(g, compressorBounds, "COMPRESSOR");

    // Right panel sections
    auto rightPanelX = contentBounds.getRight() - 260;
    auto limiterBounds = juce::Rectangle<int>(rightPanelX, 0, 260, (contentBounds.getHeight() - 60) / 2 - 8);
    auto statusBounds = juce::Rectangle<int>(rightPanelX, limiterBounds.getBottom() + 16, 260, (contentBounds.getHeight() - 60) / 2 - 8);

    drawSectionBackground(g, limiterBounds, "LIMITER");
    drawSectionBackground(g, statusBounds, "STATUS");

    // Status indicator
    auto statusCenter = statusBounds.getCentre();
    g.setColour(juce::Colour(0xff4ade80));
    g.fillEllipse(statusCenter.x - 7, statusCenter.y - 7, 14, 14);

    // Status text
    g.setColour(juce::Colour(0xff4ade80));
    g.setFont(juce::FontOptions(11.0f, juce::Font::bold));
    g.drawText("Processing\nActive", statusBounds.withTop(statusCenter.y + 15), juce::Justification::centred);
}

void CompliAudioProcessorEditor::drawSectionBackground(juce::Graphics& g, juce::Rectangle<int> bounds, const juce::String& title)
{
    // Section background
    g.setColour(juce::Colour(0x40303c60));
    g.fillRoundedRectangle(bounds.toFloat(), 14.0f);

    // Section border
    g.setColour(juce::Colour(0x1affffff));
    g.drawRoundedRectangle(bounds.toFloat(), 14.0f, 1.0f);

    // Section title
    g.setColour(juce::Colour(0xff64ffda));
    g.setFont(juce::FontOptions(15.0f, juce::Font::bold));
    g.drawText(title, bounds.removeFromTop(30), juce::Justification::centred);
}

void CompliAudioProcessorEditor::resized()
{
    auto bounds = getLocalBounds().reduced(20);
    auto headerBounds = bounds.removeFromTop(80);
    auto contentBounds = bounds.reduced(16);

    // Input section layout
    auto inputBounds = juce::Rectangle<int>(0, 0, 280, contentBounds.getHeight() - 60).withPosition(contentBounds.getTopLeft());
    auto inputContent = inputBounds.reduced(14).withTop(inputBounds.getY() + 40);

    // Preset and input selectors
    presetSelector.setBounds(inputContent.removeFromTop(30));
    inputContent.removeFromTop(8);
    inputSelector.setBounds(inputContent.removeFromTop(30));
    inputContent.removeFromTop(16);

    // Level meters
    auto meterArea = inputContent.removeFromBottom(120);
    auto meterWidth = meterArea.getWidth() / 3 - 8;

    auto inputMeterBounds = meterArea.removeFromLeft(meterWidth);
    inputMeterLabel.setBounds(inputMeterBounds.removeFromTop(20));
    inputValueLabel.setBounds(inputMeterBounds.removeFromBottom(20));
    inputMeter.setBounds(inputMeterBounds);

    meterArea.removeFromLeft(12);
    auto grMeterBounds = meterArea.removeFromLeft(meterWidth);
    grMeterLabel.setBounds(grMeterBounds.removeFromTop(20));
    grValueLabel.setBounds(grMeterBounds.removeFromBottom(20));
    grMeter.setBounds(grMeterBounds);

    meterArea.removeFromLeft(12);
    auto outputMeterBounds = meterArea;
    outputMeterLabel.setBounds(outputMeterBounds.removeFromTop(20));
    outputValueLabel.setBounds(outputMeterBounds.removeFromBottom(20));
    outputMeter.setBounds(outputMeterBounds);

    // Compressor section layout
    auto compressorBounds = juce::Rectangle<int>(300, 0, contentBounds.getWidth() - 580, contentBounds.getHeight() - 60).withPosition(contentBounds.getTopLeft().translated(300, 0));
    auto compressorContent = compressorBounds.reduced(14).withTop(compressorBounds.getY() + 40);

    // Compressor knobs in 3x2 grid
    auto knobSize = 80;
    auto knobSpacing = 20;
    auto gridWidth = 3 * knobSize + 2 * knobSpacing;
    auto gridHeight = 2 * knobSize + knobSpacing + 40; // Extra space for labels
    auto gridBounds = compressorContent.withSize(gridWidth, gridHeight).withCentre(compressorContent.getCentre());

    // Top row
    auto topRow = gridBounds.removeFromTop(knobSize + 40);
    auto thresholdBounds = topRow.removeFromLeft(knobSize);
    thresholdLabel.setBounds(thresholdBounds.removeFromTop(20));
    thresholdSlider.setBounds(thresholdBounds.removeFromTop(knobSize));

    topRow.removeFromLeft(knobSpacing);
    auto ratioBounds = topRow.removeFromLeft(knobSize);
    ratioLabel.setBounds(ratioBounds.removeFromTop(20));
    ratioSlider.setBounds(ratioBounds.removeFromTop(knobSize));

    topRow.removeFromLeft(knobSpacing);
    auto attackBounds = topRow.removeFromLeft(knobSize);
    attackLabel.setBounds(attackBounds.removeFromTop(20));
    attackSlider.setBounds(attackBounds.removeFromTop(knobSize));

    gridBounds.removeFromTop(knobSpacing);

    // Bottom row
    auto bottomRow = gridBounds.removeFromTop(knobSize + 40);
    auto releaseBounds = bottomRow.removeFromLeft(knobSize);
    releaseLabel.setBounds(releaseBounds.removeFromTop(20));
    releaseSlider.setBounds(releaseBounds.removeFromTop(knobSize));

    bottomRow.removeFromLeft(knobSpacing);
    auto makeupBounds = bottomRow.removeFromLeft(knobSize);
    makeupGainLabel.setBounds(makeupBounds.removeFromTop(20));
    makeupGainSlider.setBounds(makeupBounds.removeFromTop(knobSize));

    // Right panel layout
    auto rightPanelX = contentBounds.getRight() - 260;
    auto limiterBounds = juce::Rectangle<int>(rightPanelX, 0, 260, (contentBounds.getHeight() - 60) / 2 - 8);
    auto statusBounds = juce::Rectangle<int>(rightPanelX, limiterBounds.getBottom() + 16, 260, (contentBounds.getHeight() - 60) / 2 - 8);

    // Limiter section
    auto limiterContent = limiterBounds.reduced(14).withTop(limiterBounds.getY() + 40);
    auto limiterCenter = limiterContent.getCentre();

    auto limiterKnobBounds = juce::Rectangle<int>(knobSize, knobSize + 40).withCentre(limiterCenter.translated(0, -20));
    limiterThresholdLabel.setBounds(limiterKnobBounds.removeFromTop(20));
    limiterThresholdSlider.setBounds(limiterKnobBounds.removeFromTop(knobSize));

    auto toggleBounds = juce::Rectangle<int>(60, 30).withCentre(limiterCenter.translated(0, 40));
    limiterToggle.setBounds(toggleBounds);

    // Status bar
    auto statusBarBounds = bounds.removeFromBottom(44);
    statusBarBounds.removeFromLeft(statusBarBounds.getWidth() - 120);
    bypassButton.setBounds(statusBarBounds.reduced(8));
}
